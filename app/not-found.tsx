import Link from 'next/link'
import { IconArrowRight } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'

export default function NotFoundPage() {
    return (
        <section className="h-screen w-screen flex flex-col items-center justify-center">
            <div className="text-center space-y-4">
                <h1 className="text-6xl font-semibold tracking-tight">404</h1>
                <p className="text-muted-foreground text-lg">Page not found</p>
                <Button asChild className="mt-8">
                    <Link href="/" className="flex items-center gap-2">
                        <span>Go Home</span>
                        <IconArrowRight className="h-4 w-4" />
                    </Link>
                </Button>
            </div>
        </section>
    )
}