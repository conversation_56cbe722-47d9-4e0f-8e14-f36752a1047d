import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { ConvexHttpClient } from "convex/browser";
import { api } from "@/convex/_generated/api";
import { cookies } from "next/headers";

export async function GET(req: Request) {
  const url = new URL(req.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  const err = url.searchParams.get("error");
  const redirect = new URL("/dashboard/integrations", url.origin);
  
  // Handle OAuth errors first
  if (err) {
    redirect.searchParams.set("linear", "error");
    redirect.searchParams.set("reason", err);
    return NextResponse.redirect(redirect.toString());
  }

  if (code && state) {
    try {
      // Verify state parameter for CSRF protection
      const cookieStore = cookies();
      const storedState = cookieStore.get("linear-oauth-state")?.value;
      if (!storedState || storedState !== state) {
        throw new Error("Invalid state parameter - possible CSRF attack");
      }

      const { userId } = auth();
      if (!userId) throw new Error("Not authenticated");
      
      const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
      if (!convexUrl) throw new Error("Missing NEXT_PUBLIC_CONVEX_URL");
      
      const client = new ConvexHttpClient(convexUrl);
      const { orgId, userId: dbUserId } = await client.mutation(api.orgs.ensureUserOrg, {});
      const redirectUri = new URL("/api/oauth/linear/callback", url.origin).toString();
      
      await client.action(api.linear_oauth.exchangeCode, { 
        code, 
        redirectUri, 
        orgId, 
        userId: dbUserId 
      });
      
      redirect.searchParams.set("linear", "connected");
      
      // Clear the state cookie
      const response = NextResponse.redirect(redirect.toString());
      response.cookies.delete("linear-oauth-state");
      return response;
      
    } catch (e: any) {
      console.error("Linear OAuth callback error:", e);
      redirect.searchParams.set("linear", "error");
      redirect.searchParams.set("reason", e?.message ?? "exchange_failed");
    }
  } else {
    redirect.searchParams.set("linear", "error");
    redirect.searchParams.set("reason", "missing_code_or_state");
  }
  
  // Clear state cookie on any error
  const response = NextResponse.redirect(redirect.toString());
  response.cookies.delete("linear-oauth-state");
  return response;
}
