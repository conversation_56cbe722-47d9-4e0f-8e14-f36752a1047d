import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET(req: Request) {
  // Check authentication
  const { userId } = auth();
  if (!userId) {
    return NextResponse.redirect(new URL("/sign-in", req.url));
  }

  const clientId = process.env.LINEAR_CLIENT_ID;
  if (!clientId) {
    const errorUrl = new URL("/dashboard/integrations", req.url);
    errorUrl.searchParams.set("linear", "error");
    errorUrl.searchParams.set("reason", "missing_client_id");
    return NextResponse.redirect(errorUrl);
  }

  const url = new URL(req.url);
  const redirectUri = new URL("/api/oauth/linear/callback", url.origin).toString();
  
  // Generate secure state for CSRF protection
  const state = crypto.randomUUID();
  
  // Build Linear OAuth URL per Linear docs
  const linearAuthUrl = new URL("https://linear.app/oauth/authorize");
  linearAuthUrl.searchParams.set("response_type", "code");
  linearAuthUrl.searchParams.set("client_id", clientId);
  linearAuthUrl.searchParams.set("redirect_uri", redirectUri);
  linearAuthUrl.searchParams.set("scope", "write"); // Need write access for issue creation
  linearAuthUrl.searchParams.set("state", state);
  linearAuthUrl.searchParams.set("actor", "user"); // User-level token, not app-level

  // Store state in secure cookie for verification on callback
  const response = NextResponse.redirect(linearAuthUrl.toString());
  response.cookies.set("linear-oauth-state", state, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 600, // 10 minutes
    path: "/api/oauth/linear",
  });

  return response;
}

