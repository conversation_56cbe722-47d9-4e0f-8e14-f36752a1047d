import React from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>Header } from "./header"
import { MessageSquare, Zap, GitPullRequest } from 'lucide-react'

export default function HeroSection() {
    return (
        <>
            <HeroHeader />
            <main>
                <section className="py-16 md:py-24">
                    <div className="mx-auto max-w-4xl px-6 text-center">
                        <h1 className="mx-auto max-w-3xl text-4xl font-semibold tracking-tight sm:text-5xl">
                            Turn Telegram Messages into Linear Tasks with AI
                        </h1>
                        <p className="text-muted-foreground mx-auto my-6 max-w-2xl text-lg">
                            Send messages to your Telegram bot, let AI understand and structure them, 
                            then automatically create organized tasks in Linear.
                        </p>

                        <div className="flex items-center justify-center gap-4 mb-12">
                            <Button asChild size="lg">
                                <Link href="/dashboard">
                                    Get Started
                                </Link>
                            </Button>
                            <Button asChild size="lg" variant="outline">
                                <Link href="#pricing">
                                    View Pricing
                                </Link>
                            </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                            <div className="flex flex-col items-center gap-4 p-6">
                                <div className="p-3 bg-primary/10 rounded-lg">
                                    <MessageSquare className="h-6 w-6 text-primary" />
                                </div>
                                <div className="text-center">
                                    <h3 className="font-semibold mb-2">Message</h3>
                                    <p className="text-sm text-muted-foreground">Send tasks via Telegram</p>
                                </div>
                            </div>
                            <div className="flex flex-col items-center gap-4 p-6">
                                <div className="p-3 bg-primary/10 rounded-lg">
                                    <Zap className="h-6 w-6 text-primary" />
                                </div>
                                <div className="text-center">
                                    <h3 className="font-semibold mb-2">Process</h3>
                                    <p className="text-sm text-muted-foreground">AI structures your intent</p>
                                </div>
                            </div>
                            <div className="flex flex-col items-center gap-4 p-6">
                                <div className="p-3 bg-primary/10 rounded-lg">
                                    <GitPullRequest className="h-6 w-6 text-primary" />
                                </div>
                                <div className="text-center">
                                    <h3 className="font-semibold mb-2">Create</h3>
                                    <p className="text-sm text-muted-foreground">Issues appear in Linear</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </>
    )
}