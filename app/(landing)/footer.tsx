import { ChatMaxingIconColoured } from '@/components/logo'
import Link from 'next/link'

export default function FooterSection() {
    return (
        <footer className="border-t py-12">
            <div className="mx-auto max-w-4xl px-6">
                <div className="flex flex-col items-center gap-4">
                    <Link
                        href="/"
                        aria-label="go home"
                        className="flex gap-2 items-center">
                        <ChatMaxingIconColoured />
                        <span className="text-lg font-semibold">BuddyTasks Pro</span>
                    </Link>
                    <p className="text-center text-sm text-muted-foreground max-w-md">
                        Transform your Telegram messages into organized Linear tasks with the power of AI.
                    </p>
                    <div className="flex gap-6 text-sm">
                        <Link href="#pricing" className="text-muted-foreground hover:text-primary">
                            Pricing
                        </Link>
                        <Link href="/dashboard" className="text-muted-foreground hover:text-primary">
                            Dashboard
                        </Link>
                        <Link href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary">
                            Support
                        </Link>
                    </div>
                    <span className="text-muted-foreground text-xs text-center">
                        © {new Date().getFullYear()} BuddyTasks Pro. All rights reserved.
                    </span>
                </div>
            </div>
        </footer>
    )
}
