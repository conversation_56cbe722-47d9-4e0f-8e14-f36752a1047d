@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.5rem;
  /* BuddyTasks Pro Brand Colors */
  --background: oklch(0.999 0.002 308.09); /* #ffeffe */
  --foreground: oklch(0.275 0.081 229.24); /* #094067 - headlines */
  --card: oklch(0.999 0.002 308.09); /* #ffeffe - same as background */
  --card-foreground: oklch(0.275 0.081 229.24); /* #094067 */
  --popover: oklch(0.999 0.002 308.09);
  --popover-foreground: oklch(0.275 0.081 229.24);
  /* Primary brand color - BuddyTasks Pro blue */
  --primary: oklch(0.643 0.141 223.88); /* #3da9fc */
  --primary-foreground: oklch(0.999 0.002 308.09); /* #ffeffe - button text */
  --secondary: oklch(0.96 0.008 246.84);
  --secondary-foreground: oklch(0.275 0.081 229.24);
  --muted: oklch(0.96 0.008 246.84);
  --muted-foreground: oklch(0.473 0.024 226.77); /* #5f6c7b - paragraph text */
  --accent: oklch(0.643 0.141 223.88); /* Use primary as accent */
  --accent-foreground: oklch(0.999 0.002 308.09);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.012 246.84);
  --input: oklch(0.95 0.008 246.84);
  --ring: oklch(0.643 0.141 223.88); /* Primary color for focus rings */
  --chart-1: oklch(0.81 0.1 252);
  --chart-2: oklch(0.62 0.19 260);
  --chart-3: oklch(0.55 0.22 263);
  --chart-4: oklch(0.49 0.22 264);
  --chart-5: oklch(0.42 0.18 266);
  --sidebar: oklch(0.999 0.002 308.09); /* Same as background */
  --sidebar-foreground: oklch(0.275 0.081 229.24); /* Same as foreground */
  --sidebar-primary: oklch(0.643 0.141 223.88); /* Primary brand color */
  --sidebar-primary-foreground: oklch(0.999 0.002 308.09);
  --sidebar-accent: oklch(0.96 0.008 246.84);
  --sidebar-accent-foreground: oklch(0.275 0.081 229.24);
  --sidebar-border: oklch(0.92 0.012 246.84);
  --sidebar-ring: oklch(0.643 0.141 223.88);

  --shadow-color: oklch(0 0 0);
  --shadow-2xs: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.05);
  --shadow-xs: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.05);
  --shadow-sm: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1), 0 1px 2px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1), 0 1px 2px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-md: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1), 0 2px 4px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-lg: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1), 0 4px 6px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-xl: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1), 0 8px 10px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-2xl: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1);
}

.dark {
  /* Dark mode with brand colors maintained */
  --background: oklch(0.145 0.020 229.24); /* Dark blue-gray based on headline color */
  --foreground: oklch(0.985 0.002 308.09); /* Light variant of background */
  --card: oklch(0.200 0.025 229.24); /* Slightly lighter than background */
  --card-foreground: oklch(0.985 0.002 308.09);
  --popover: oklch(0.180 0.022 229.24);
  --popover-foreground: oklch(0.985 0.002 308.09);
  /* Keep primary brand color consistent in dark mode */
  --primary: oklch(0.643 0.141 223.88); /* #3da9fc - same as light mode */
  --primary-foreground: oklch(0.999 0.002 308.09); /* #ffeffe */
  --secondary: oklch(0.250 0.025 229.24);
  --secondary-foreground: oklch(0.985 0.002 308.09);
  --muted: oklch(0.250 0.025 229.24);
  --muted-foreground: oklch(0.700 0.020 226.77); /* Lighter version of paragraph color */
  --accent: oklch(0.643 0.141 223.88); /* Same primary */
  --accent-foreground: oklch(0.999 0.002 308.09);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 15%);
  --input: oklch(1 0 0 / 20%);
  --ring: oklch(0.643 0.141 223.88); /* Primary color for focus rings */
  --chart-1: oklch(0.81 0.1 252);
  --chart-2: oklch(0.62 0.19 260);
  --chart-3: oklch(0.55 0.22 263);
  --chart-4: oklch(0.49 0.22 264);
  --chart-5: oklch(0.42 0.18 266);
  --sidebar: oklch(0.200 0.025 229.24); /* Same as card */
  --sidebar-foreground: oklch(0.985 0.002 308.09);
  --sidebar-primary: oklch(0.643 0.141 223.88); /* Primary brand color */
  --sidebar-primary-foreground: oklch(0.999 0.002 308.09);
  --sidebar-accent: oklch(0.250 0.025 229.24);
  --sidebar-accent-foreground: oklch(0.985 0.002 308.09);
  --sidebar-border: oklch(1 0 0 / 15%);
  --sidebar-ring: oklch(0.643 0.141 223.88);

  --shadow-color: oklch(0 0 0);
}

@theme inline {
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --animate-pulse: pulse var(--duration) ease-out infinite;
  @keyframes pulse {
  0%, 100% {
    boxShadow: 0 0 0 0 var(--pulse-color);
    }
  50% {
    boxShadow: 0 0 0 8px var(--pulse-color);
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    /* Base font size and line height following design system */
    font-size: 1rem; /* 16px - Size 3: Body text */
    line-height: 1.5;
    font-weight: 400; /* Regular weight */
  }
  
  /* Typography System: 4 Sizes, 2 Weights */
  /* Size 1: Large headings */
  h1, .text-size-1 {
    font-size: 2rem; /* 32px */
    line-height: 1.25;
    font-weight: 600; /* Semibold */
    color: var(--foreground); /* #094067 - headlines */
  }
  
  /* Size 2: Subheadings */
  h2, h3, .text-size-2 {
    font-size: 1.5rem; /* 24px */
    line-height: 1.33;
    font-weight: 600; /* Semibold */
    color: var(--foreground); /* #094067 - headlines */
  }
  
  /* Size 3: Body text (default) */
  p, .text-size-3 {
    font-size: 1rem; /* 16px */
    line-height: 1.5;
    font-weight: 400; /* Regular */
    color: var(--muted-foreground); /* #5f6c7b - paragraph */
  }
  
  /* Size 4: Small text/labels */
  small, .text-size-4 {
    font-size: 0.875rem; /* 14px */
    line-height: 1.43;
    font-weight: 400; /* Regular */
    color: var(--muted-foreground); /* #5f6c7b - paragraph */
  }
  
  /* Ensure headings use semibold weight */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600; /* Semibold only */
    color: var(--foreground); /* Headlines color */
  }
  
  /* Override default button styling to maintain design system */
  button {
    font-weight: 400; /* Regular weight for buttons */
  }
  
  /* Special case: button text should be regular unless specifically emphasized */
  button.font-semibold {
    font-weight: 600;
  }
}

.cpu-architecture {
  offset-anchor: 10px 0px;
  animation: animation-path;
  animation-iteration-count: infinite;
  animation-timing-function: cubic-bezier(0.75, -0.01, 0, 0.99);
}

.cpu-line-1 {
  offset-path: path("M 10 20 h 79.5 q 5 0 5 5 v 30");
  animation-duration: 5s;
  animation-delay: 1s;
}

.cpu-line-2 {
  offset-path: path("M 180 10 h -69.7 q -5 0 -5 5 v 40");
  animation-delay: 6s;
  animation-duration: 2s;
}

.cpu-line-3 {
  offset-path: path("M 130 20 v 21.8 q 0 5 -5 5 h -25");
  animation-delay: 4s;
  animation-duration: 6s;
}

.cpu-line-4 {
  offset-path: path("M 170 80 v -21.8 q 0 -5 -5 -5 h -65");
  animation-delay: 3s;
  animation-duration: 3s;
}

.cpu-line-5 {
  offset-path: path(
    "M 135 65 h 15 q 5 0 5 5 v 10 q 0 5 -5 5 h -39.8 q -5 0 -5 -5 v -35"
  );
  animation-delay: 9s;
  animation-duration: 4s;
}

.cpu-line-6 {
  offset-path: path("M 94.8 95 v -46");
  animation-delay: 3s;
  animation-duration: 7s;
}

.cpu-line-7 {
  offset-path: path(
    "M 88 88 v -15 q 0 -5 -5 -5 h -10 q -5 0 -5 -5 v -5 q 0 -5 5 -5 h 28"
  );
  animation-delay: 4s;
  animation-duration: 4s;
}

.cpu-line-8 {
  offset-path: path("M 30 30 h 25 q 5 0 5 5 v 6.5 q 0 5 5 5 h 35");
  animation-delay: 3s;
  animation-duration: 3s;
}

@keyframes animation-path {
  0% {
    offset-distance: 0%;
  }
  100% {
    offset-distance: 100%;
  }
}