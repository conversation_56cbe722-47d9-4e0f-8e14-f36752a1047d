"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useS<PERSON><PERSON>, DragEndEvent } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Plus, Edit, Trash2, GripVertical, TestTube, Play, CheckCircle2, XCircle } from "lucide-react";
import { Id } from "@/convex/_generated/dataModel";

interface RoutingRule {
  _id: Id<"routingRules">;
  name: string;
  isActive: boolean;
  priority: number;
  match: {
    chatId?: string;
    includes?: string[];
    regex?: string;
  };
  action: {
    linearTeamId?: string;
    labels?: string[];
    template?: {
      title: string;
      description: string;
    };
  };
}

interface SortableRuleProps {
  rule: RoutingRule;
  onEdit: (rule: RoutingRule) => void;
  onDelete: (id: Id<"routingRules">) => void;
  onToggle: (id: Id<"routingRules">, isActive: boolean) => void;
}

function SortableRule({ rule, onEdit, onDelete, onToggle }: SortableRuleProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: rule._id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} className="touch-none">
      <Card className={`${!rule.isActive ? 'opacity-60' : ''} ${isDragging ? 'shadow-lg' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div {...attributes} {...listeners} className="cursor-grab">
              <GripVertical className="h-4 w-4 text-muted-foreground" />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-semibold">{rule.name}</h3>
                <Badge variant={rule.isActive ? "default" : "secondary"}>
                  {rule.isActive ? "Active" : "Inactive"}
                </Badge>
                <Badge variant="outline">Priority {rule.priority}</Badge>
              </div>
              
              <div className="space-y-2 text-sm text-muted-foreground">
                {rule.match.chatId && (
                  <div>Chat ID: <code className="bg-muted px-2 py-1 rounded text-xs">{rule.match.chatId}</code></div>
                )}
                {rule.match.includes && rule.match.includes.length > 0 && (
                  <div>Keywords: {rule.match.includes.map(k => 
                    <code key={k} className="bg-muted px-2 py-1 rounded text-xs mr-2">{k}</code>
                  )}</div>
                )}
                {rule.match.regex && (
                  <div>Regex: <code className="bg-muted px-2 py-1 rounded text-xs">{rule.match.regex}</code></div>
                )}
                {rule.action.linearTeamId && (
                  <div>Team: <code className="bg-muted px-2 py-1 rounded text-xs">{rule.action.linearTeamId}</code></div>
                )}
                {rule.action.labels && rule.action.labels.length > 0 && (
                  <div>Labels: {rule.action.labels.map(l => 
                    <Badge key={l} variant="outline" className="mr-2 text-xs">{l}</Badge>
                  )}</div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Switch
                checked={rule.isActive}
                onCheckedChange={(checked) => onToggle(rule._id, checked)}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(rule)}
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(rule._id)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface RuleFormData {
  name: string;
  isActive: boolean;
  match: {
    chatId: string;
    includes: string;
    regex: string;
  };
  action: {
    linearTeamId: string;
    labels: string;
    template: {
      title: string;
      description: string;
    };
  };
  priority?: number;
}

const defaultFormData: RuleFormData = {
  name: "",
  isActive: true,
  match: {
    chatId: "",
    includes: "",
    regex: "",
  },
  action: {
    linearTeamId: "",
    labels: "",
    template: {
      title: "",
      description: "",
    },
  },
};

export default function RoutingPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<RoutingRule | null>(null);
  const [formData, setFormData] = useState<RuleFormData>(defaultFormData);
  const [testMessage, setTestMessage] = useState("");
  const [testChatId, setTestChatId] = useState("");
  const [message, setMessage] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  const rules = useQuery(api.routing.listRoutingRules);
  const testResults = useQuery(api.routing.testRoutingRule, 
    testMessage ? { testMessage, testChatId: testChatId || undefined } : "skip"
  );
  
  const createRule = useMutation(api.routing.createRoutingRule);
  const updateRule = useMutation(api.routing.updateRoutingRule);
  const deleteRule = useMutation(api.routing.deleteRoutingRule);
  const reorderRules = useMutation(api.routing.reorderRoutingRules);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over || !rules) return;
    
    if (active.id !== over.id) {
      const oldIndex = rules.findIndex((rule) => rule._id === active.id);
      const newIndex = rules.findIndex((rule) => rule._id === over.id);
      
      const reorderedRules = [...rules];
      const [removed] = reorderedRules.splice(oldIndex, 1);
      reorderedRules.splice(newIndex, 0, removed);
      
      const ruleIds = reorderedRules.map(rule => rule._id);
      
      try {
        await reorderRules({ ruleIds });
        setMessage("✅ Rules reordered successfully!");
      } catch (error: any) {
        setMessage(`❌ Failed to reorder rules: ${error.message}`);
      }
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setMessage(null);

      const processedData = {
        name: formData.name,
        isActive: formData.isActive,
        match: {
          ...(formData.match.chatId && { chatId: formData.match.chatId }),
          ...(formData.match.includes && { includes: formData.match.includes.split(',').map(s => s.trim()).filter(Boolean) }),
          ...(formData.match.regex && { regex: formData.match.regex }),
        },
        action: {
          ...(formData.action.linearTeamId && { linearTeamId: formData.action.linearTeamId }),
          ...(formData.action.labels && { labels: formData.action.labels.split(',').map(s => s.trim()).filter(Boolean) }),
          ...(formData.action.template.title && formData.action.template.description && {
            template: formData.action.template
          }),
        },
        ...(formData.priority !== undefined && { priority: formData.priority }),
      };

      if (editingRule) {
        await updateRule({ 
          ruleId: editingRule._id,
          ...processedData
        });
        setMessage("✅ Rule updated successfully!");
      } else {
        await createRule(processedData);
        setMessage("✅ Rule created successfully!");
      }

      setIsCreateDialogOpen(false);
      setEditingRule(null);
      setFormData(defaultFormData);
    } catch (error: any) {
      setMessage(`❌ Failed to save rule: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  const handleEdit = (rule: RoutingRule) => {
    setEditingRule(rule);
    setFormData({
      name: rule.name,
      isActive: rule.isActive,
      match: {
        chatId: rule.match.chatId || "",
        includes: rule.match.includes?.join(', ') || "",
        regex: rule.match.regex || "",
      },
      action: {
        linearTeamId: rule.action.linearTeamId || "",
        labels: rule.action.labels?.join(', ') || "",
        template: {
          title: rule.action.template?.title || "",
          description: rule.action.template?.description || "",
        },
      },
      priority: rule.priority,
    });
    setIsCreateDialogOpen(true);
  };

  const handleDelete = async (id: Id<"routingRules">) => {
    if (!confirm("Are you sure you want to delete this rule?")) return;
    
    try {
      await deleteRule({ ruleId: id });
      setMessage("✅ Rule deleted successfully!");
    } catch (error: any) {
      setMessage(`❌ Failed to delete rule: ${error.message}`);
    }
  };

  const handleToggle = async (id: Id<"routingRules">, isActive: boolean) => {
    try {
      await updateRule({ ruleId: id, isActive });
      setMessage(`✅ Rule ${isActive ? 'enabled' : 'disabled'} successfully!`);
    } catch (error: any) {
      setMessage(`❌ Failed to update rule: ${error.message}`);
    }
  };

  const handleCreate = () => {
    setEditingRule(null);
    setFormData(defaultFormData);
    setIsCreateDialogOpen(true);
  };

  const clearTest = () => {
    setTestMessage("");
    setTestChatId("");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Routing Rules</h1>
          <p className="text-sm text-muted-foreground">
            Configure how messages are processed and routed to Linear teams
          </p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="h-4 w-4 mr-2" />
          Add Rule
        </Button>
      </div>

      {message && (
        <Alert className={message.includes("❌") ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"}>
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      {/* Test Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-4 w-4" />
            Test Rules
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="testMessage">Test Message</Label>
              <Textarea
                id="testMessage"
                placeholder="Enter a message to test against your rules..."
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="testChatId">Chat ID (Optional)</Label>
              <Input
                id="testChatId"
                placeholder="e.g., -1001234567890"
                value={testChatId}
                onChange={(e) => setTestChatId(e.target.value)}
              />
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={clearTest}
                >
                  Clear
                </Button>
              </div>
            </div>
          </div>

          {testResults && testResults.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">Test Results:</h4>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    <div className="mt-0.5">
                      {result.matches ? (
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{result.rule.name}</span>
                        <Badge variant="outline">Priority {result.rule.priority}</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {result.reasons.map((reason, i) => (
                          <div key={i}>{reason}</div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Rules List */}
      <div className="space-y-4">
        {rules && rules.length > 0 ? (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={rules.map(rule => rule._id)} strategy={verticalListSortingStrategy}>
              {rules.map((rule) => (
                <SortableRule
                  key={rule._id}
                  rule={rule}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onToggle={handleToggle}
                />
              ))}
            </SortableContext>
          </DndContext>
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground">No routing rules configured</p>
              <p className="text-sm text-muted-foreground mt-1">
                Create your first rule to start routing messages automatically
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Create/Edit Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{editingRule ? "Edit Rule" : "Create Rule"}</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Basic Settings */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Rule Name</Label>
                <Input
                  id="name"
                  placeholder="e.g., Bug Reports"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
                <Label htmlFor="isActive">Rule is active</Label>
              </div>
            </div>

            {/* Match Conditions */}
            <div className="space-y-4">
              <h3 className="font-medium">Match Conditions</h3>
              
              <div className="space-y-2">
                <Label htmlFor="chatId">Specific Chat ID (Optional)</Label>
                <Input
                  id="chatId"
                  placeholder="e.g., -1001234567890"
                  value={formData.match.chatId}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    match: { ...prev.match, chatId: e.target.value }
                  }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="includes">Keywords (Optional)</Label>
                <Input
                  id="includes"
                  placeholder="bug, error, issue (comma-separated)"
                  value={formData.match.includes}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    match: { ...prev.match, includes: e.target.value }
                  }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="regex">Regex Pattern (Optional)</Label>
                <Input
                  id="regex"
                  placeholder="e.g., /(urgent|critical)/i"
                  value={formData.match.regex}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    match: { ...prev.match, regex: e.target.value }
                  }))}
                />
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <h3 className="font-medium">Actions</h3>
              
              <div className="space-y-2">
                <Label htmlFor="linearTeamId">Linear Team ID (Optional)</Label>
                <Input
                  id="linearTeamId"
                  placeholder="e.g., team_abc123"
                  value={formData.action.linearTeamId}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    action: { ...prev.action, linearTeamId: e.target.value }
                  }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="labels">Labels (Optional)</Label>
                <Input
                  id="labels"
                  placeholder="bug, urgent, customer (comma-separated)"
                  value={formData.action.labels}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    action: { ...prev.action, labels: e.target.value }
                  }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Issue Template (Optional)</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="Title template"
                    value={formData.action.template.title}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      action: { 
                        ...prev.action, 
                        template: { ...prev.action.template, title: e.target.value }
                      }
                    }))}
                  />
                  <Textarea
                    placeholder="Description template"
                    value={formData.action.template.description}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      action: { 
                        ...prev.action, 
                        template: { ...prev.action.template, description: e.target.value }
                      }
                    }))}
                  />
                </div>
              </div>

              {editingRule && (
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority (Optional)</Label>
                  <Input
                    id="priority"
                    type="number"
                    placeholder="e.g., 1"
                    value={formData.priority || ""}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      priority: e.target.value ? parseInt(e.target.value) : undefined
                    }))}
                  />
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button 
                variant="outline" 
                onClick={() => setIsCreateDialogOpen(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={saving || !formData.name}>
                {saving ? "Saving..." : (editingRule ? "Update Rule" : "Create Rule")}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}