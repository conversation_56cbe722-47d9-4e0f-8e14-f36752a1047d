"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"

import { useIsMobile } from "@/hooks/use-mobile"
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group"

export const description = "Activity analytics chart showing messages and tasks over time"

const chartConfig = {
  activity: {
    label: "Activity",
  },
  messages: {
    label: "Messages",
    color: "#3b82f6", // blue-500
  },
  issues: {
    label: "Issues Created", 
    color: "#10b981", // emerald-500
  },
  aiRuns: {
    label: "AI Runs",
    color: "#8b5cf6", // violet-500
  },
  webhooks: {
    label: "Webhook Events",
    color: "#f59e0b", // amber-500
  },
} satisfies ChartConfig

export function ChartAreaInteractive() {
  const isMobile = useIsMobile()
  const activityHistory = useQuery(api.queries.getActivityHistory, { days: 7 })

  if (!activityHistory) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] w-full animate-pulse bg-muted rounded-lg" />
        </CardContent>
      </Card>
    )
  }

  const totalActivity = activityHistory.reduce((sum, day) => 
    sum + day.messages + day.issues + day.aiRuns + day.webhooks, 0
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>
          Last 7 days • {totalActivity} total events
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="h-[200px] w-full"
        >
          <AreaChart data={activityHistory}>
            <defs>
              <linearGradient id="fillMessages" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-messages)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-messages)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillIssues" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-issues)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-issues)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillAiRuns" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-aiRuns)"
                  stopOpacity={0.6}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-aiRuns)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillWebhooks" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-webhooks)"
                  stopOpacity={0.6}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-webhooks)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                })
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isMobile ? -1 : activityHistory.length - 1}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    })
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="webhooks"
              type="natural"
              fill="url(#fillWebhooks)"
              stroke="var(--color-webhooks)"
              stackId="a"
            />
            <Area
              dataKey="aiRuns"
              type="natural"
              fill="url(#fillAiRuns)"
              stroke="var(--color-aiRuns)"
              stackId="a"
            />
            <Area
              dataKey="issues"
              type="natural"
              fill="url(#fillIssues)"
              stroke="var(--color-issues)"
              stackId="a"
            />
            <Area
              dataKey="messages"
              type="natural"
              fill="url(#fillMessages)"
              stroke="var(--color-messages)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
