"use client";

import { IconMessageCircle, IconListDetails, IconBrandOpenai, IconWebhook, IconTrendingUp } from "@tabler/icons-react"
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

export function SectionCards() {
  const activityStats = useQuery(api.queries.getActivityStats);

  if (!activityStats) {
    return (
      <div className="grid grid-cols-1 gap-4 px-4 lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <CardDescription>Loading...</CardDescription>
              <CardTitle className="text-2xl font-semibold">---</CardTitle>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  }

  const cards = [
    {
      title: "Messages Processed",
      value: activityStats.messages,
      description: "Incoming messages handled",
      icon: IconMessageCircle,
      trend: "24 hours",
    },
    {
      title: "Issues Created",
      value: activityStats.issues,
      description: "Linear tasks generated",
      icon: IconListDetails,
      trend: "24 hours",
    },
    {
      title: "AI Runs",
      value: activityStats.aiRuns,
      description: "OpenRouter API calls",
      icon: IconBrandOpenai,
      trend: "24 hours",
    },
    {
      title: "Webhook Events",
      value: activityStats.webhooks,
      description: "External integrations",
      icon: IconWebhook,
      trend: "24 hours",
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 px-4 lg:px-6">
      {cards.map((card, index) => {
        const IconComponent = card.icon;
        return (
          <Card key={index}>
            <CardHeader className="pb-4">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-primary/10">
                  <IconComponent className="size-4 text-primary" />
                </div>
                <CardDescription className="text-xs">{card.title}</CardDescription>
              </div>
              <CardTitle className="text-xl font-semibold tabular-nums">
                {card.value.toLocaleString()}
              </CardTitle>
            </CardHeader>
          </Card>
        );
      })}
    </div>
  )
}
