"use client";

import { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  MessageSquare, 
  GitPullRequest, 
  Zap, 
  Webhook, 
  Search,
  ExternalLink,
  Clock,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Loader,
  TrendingUp,
  ChevronDown,
  ChevronRight,
  Filter,
  Copy
} from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function LogsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("messages");
  const [webhookSourceFilter, setWebhookSourceFilter] = useState("all");
  const [webhookStatusFilter, setWebhookStatusFilter] = useState("all");

  // Data queries
  const inboundMessages = useQuery(api.queries.listRecentInbound);
  const issues = useQuery(api.queries.listRecentIssues);
  const aiRuns = useQuery(api.queries.listRecentAiRuns);
  const webhookEvents = useQuery(api.queries.listRecentWebhookEvents);

  // Filter data based on search term
  const filteredMessages = useMemo(() => {
    if (!inboundMessages || !searchTerm) return inboundMessages || [];
    return inboundMessages.filter(m => 
      m.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      m.chatId.includes(searchTerm) ||
      m.status.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [inboundMessages, searchTerm]);

  const filteredIssues = useMemo(() => {
    if (!issues || !searchTerm) return issues || [];
    return issues.filter(i => 
      i.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      i.linearIssueId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      i.status.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [issues, searchTerm]);

  const filteredAiRuns = useMemo(() => {
    if (!aiRuns || !searchTerm) return aiRuns || [];
    return aiRuns.filter(run => 
      (run.promptPreview?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
      (run.responsePreview?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
      run.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      run.status.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [aiRuns, searchTerm]);

  const filteredWebhooks = useMemo(() => {
    if (!webhookEvents) return [];
    
    let filtered = webhookEvents;
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(event => 
        event.source.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (event.error?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false)
      );
    }
    
    // Apply source filter
    if (webhookSourceFilter !== "all") {
      filtered = filtered.filter(event => event.source === webhookSourceFilter);
    }
    
    // Apply status filter
    if (webhookStatusFilter !== "all") {
      if (webhookStatusFilter === "handled") {
        filtered = filtered.filter(event => event.handled && !event.error);
      } else if (webhookStatusFilter === "unhandled") {
        filtered = filtered.filter(event => !event.handled && !event.error);
      } else if (webhookStatusFilter === "error") {
        filtered = filtered.filter(event => event.error);
      }
    }
    
    return filtered;
  }, [webhookEvents, searchTerm, webhookSourceFilter, webhookStatusFilter]);

  function formatTime(timestamp: number) {
    return new Date(timestamp).toLocaleString();
  }

  function formatRelativeTime(timestamp: number) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return "Just now";
  }

  function StatusBadge({ status, error }: { status: string; error?: string }) {
    if (error) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <XCircle className="h-3 w-3" />
        Error
      </Badge>;
    }

    switch (status.toLowerCase()) {
      case "processed":
      case "created":
      case "succeeded":
        return <Badge variant="secondary" className="flex items-center gap-1 bg-primary/10 text-primary border-primary/20">
          <CheckCircle2 className="h-3 w-3" />
          {status}
        </Badge>;
      case "queued":
      case "in_progress":
        return <Badge variant="secondary" className="flex items-center gap-1 bg-primary/10 text-primary border-primary/20">
          <Loader className="h-3 w-3" />
          {status}
        </Badge>;
      case "failed":
      case "error":
        return <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          {status}
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  }

  function formatJson(obj: any) {
    try {
      return JSON.stringify(obj, null, 2);
    } catch {
      return String(obj);
    }
  }

  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    });
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-semibold">Activity Logs</h1>
        <p className="text-sm text-muted-foreground">Recent activity across all integrations</p>
      </div>


      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="messages">Messages</TabsTrigger>
          <TabsTrigger value="issues">Issues</TabsTrigger>
          <TabsTrigger value="ai">AI Runs</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
        </TabsList>

        <TabsContent value="messages" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Inbound Messages
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredMessages.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-8">
                    No messages found.
                  </p>
                ) : (
                  filteredMessages.map((message) => (
                    <div key={message._id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Chat {message.chatId}</span>
                          <StatusBadge status={message.status} error={message.error} />
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {formatRelativeTime(message._creationTime!)}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {message.text}
                      </p>
                      {message.error && (
                        <p className="text-xs text-destructive bg-destructive/5 p-2 rounded">
                          {message.error}
                        </p>
                      )}
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>From: {message.fromTelegramUserId}</span>
                        <span>Update ID: {message.telegramUpdateId}</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="issues" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitPullRequest className="h-5 w-5" />
                Created Issues
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredIssues.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-8">
                    No issues found.
                  </p>
                ) : (
                  filteredIssues.map((issue) => (
                    <div key={issue._id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{issue.title}</h4>
                        <div className="flex items-center gap-2">
                          <StatusBadge status={issue.status} error={issue.error} />
                          <span className="text-xs text-muted-foreground">
                            {formatRelativeTime(issue.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        {issue.linearIssueId ? (
                          <a 
                            href={issue.linearUrl} 
                            target="_blank" 
                            rel="noreferrer"
                            className="flex items-center gap-1 text-sm text-primary hover:underline"
                          >
                            {issue.linearIssueId}
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        ) : (
                          <span className="text-sm text-muted-foreground">No Linear ID</span>
                        )}
                        {issue.teamId && (
                          <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                            Team: {issue.teamId}
                          </span>
                        )}
                      </div>
                      {issue.error && (
                        <p className="text-xs text-destructive bg-destructive/5 p-2 rounded">
                          {issue.error}
                        </p>
                      )}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                AI Processing Runs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredAiRuns.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-8">
                    No AI runs found.
                  </p>
                ) : (
                  filteredAiRuns.map((run) => (
                    <div key={run._id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{run.model}</span>
                          <StatusBadge status={run.status} error={run.error} />
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {formatRelativeTime(run.createdAt)}
                        </span>
                      </div>
                      {run.promptPreview && (
                        <div className="space-y-2">
                          <label className="text-xs font-medium text-muted-foreground">Prompt:</label>
                          <p className="text-xs bg-muted/50 p-2 rounded line-clamp-2">
                            {run.promptPreview}
                          </p>
                        </div>
                      )}
                      {run.responsePreview && (
                        <div className="space-y-2">
                          <label className="text-xs font-medium text-muted-foreground">Response:</label>
                          <p className="text-xs bg-primary/5 p-2 rounded line-clamp-2">
                            {run.responsePreview}
                          </p>
                        </div>
                      )}
                      {run.usage && (
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>Prompt: {run.usage.prompt_tokens} tokens</span>
                          <span>Response: {run.usage.completion_tokens} tokens</span>
                          <span>Total: {run.usage.total_tokens} tokens</span>
                        </div>
                      )}
                      {run.error && (
                        <p className="text-xs text-destructive bg-destructive/5 p-2 rounded">
                          {run.error}
                        </p>
                      )}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Webhook className="h-5 w-5" />
                  Webhook Events
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <Select value={webhookSourceFilter} onValueChange={setWebhookSourceFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sources</SelectItem>
                      <SelectItem value="telegram">Telegram</SelectItem>
                      <SelectItem value="linear">Linear</SelectItem>
                      <SelectItem value="clerk">Clerk</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={webhookStatusFilter} onValueChange={setWebhookStatusFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="handled">Handled</SelectItem>
                      <SelectItem value="unhandled">Unhandled</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredWebhooks.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-8">
                    No webhook events found.
                  </p>
                ) : (
                  filteredWebhooks.map((event) => (
                    <Collapsible key={event._id}>
                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium capitalize">{event.source}</span>
                            <Badge variant={event.handled ? "secondary" : "outline"}>
                              {event.handled ? "Handled" : "Unhandled"}
                            </Badge>
                            {event.error && (
                              <Badge variant="destructive">Error</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-muted-foreground">
                              {formatRelativeTime(event.receivedAt)}
                            </span>
                            <CollapsibleTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <ChevronDown className="h-4 w-4" />
                              </Button>
                            </CollapsibleTrigger>
                          </div>
                        </div>
                        
                        {/* Status Summary */}
                        {event.error ? (
                          <p className="text-xs text-destructive bg-destructive/5 p-2 rounded">
                            {event.error}
                          </p>
                        ) : (
                          <p className="text-xs text-primary bg-primary/5 p-2 rounded">
                            Successfully processed
                          </p>
                        )}
                        
                        <div className="text-xs text-muted-foreground">
                          <span>Received: {formatTime(event.receivedAt)}</span>
                        </div>

                        {/* Expandable Details */}
                        <CollapsibleContent className="space-y-4 pt-4">
                          {/* Headers Section */}
                          {event.headers && (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">Request Headers</h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(formatJson(event.headers))}
                                  className="text-xs"
                                >
                                  <Copy className="h-3 w-3 mr-1" />
                                  Copy
                                </Button>
                              </div>
                              <div className="bg-muted/30 p-4 rounded-lg">
                                <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                                  {formatJson(event.headers)}
                                </pre>
                              </div>
                            </div>
                          )}

                          {/* Body Section */}
                          {event.body && (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">Request Body</h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(formatJson(event.body))}
                                  className="text-xs"
                                >
                                  <Copy className="h-3 w-3 mr-1" />
                                  Copy
                                </Button>
                              </div>
                              <div className="bg-muted/30 p-4 rounded-lg max-h-64 overflow-y-auto">
                                <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                                  {formatJson(event.body)}
                                </pre>
                              </div>
                            </div>
                          )}

                          {/* Metadata */}
                          <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                            <div>
                              <span className="text-xs font-medium text-muted-foreground">Source:</span>
                              <span className="text-xs ml-2">{event.source}</span>
                            </div>
                            <div>
                              <span className="text-xs font-medium text-muted-foreground">Status:</span>
                              <span className="text-xs ml-2">{event.handled ? "Handled" : "Unhandled"}</span>
                            </div>
                            {event.orgId && (
                              <div className="col-span-2">
                                <span className="text-xs font-medium text-muted-foreground">Org ID:</span>
                                <span className="text-xs ml-2 font-mono">{event.orgId}</span>
                              </div>
                            )}
                          </div>
                        </CollapsibleContent>
                      </div>
                    </Collapsible>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

