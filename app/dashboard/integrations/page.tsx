"use client";

import { useState, useEffect } from "react";
import { useAction, useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle2, XCircle, ExternalLink } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function IntegrationsPage() {
  const { user } = useUser();
  const [openRouterKey, setOpenRouterKey] = useState("");
  const saveSecret = useAction(api.secrets.saveSecret);
  const ensureUserOrg = useMutation(api.orgs.ensureUserOrg);
  const disconnectIntegration = useMutation(api.connections.disconnectIntegration);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [botToken, setBotToken] = useState("");
  const [webhookSecret, setWebhookSecret] = useState("");
  // Access nested Convex modules with bracket syntax to satisfy TS types
  const connectBot = useAction(api["integrations/telegram"].connectBot);
  
  // Get connection statuses
  const connectionStatuses = useQuery(api.queries.getConnectionStatuses);
  const connectionDetails = useQuery(api.queries.getConnectionDetails);
  const telegramLinkingStatus = useQuery(api.queries.getTelegramLinkingStatus);
  const botUsername = useQuery(api.queries.getBotUsername);
  
  // Telegram linking
  const [linkingInProgress, setLinkingInProgress] = useState(false);
  const generateNonce = useMutation(api.connections.generateTelegramLinkingNonce);

  // Check for URL parameters for OAuth callback messages
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const linearStatus = params.get("linear");
    const reason = params.get("reason");
    
    if (linearStatus === "connected") {
      setMessage("✅ Linear connected successfully!");
    } else if (linearStatus === "error") {
      setMessage(`❌ Linear connection failed: ${reason || "unknown error"}`);
    }
    
    // Clean up URL parameters
    if (params.has("linear") || params.has("reason")) {
      const newUrl = window.location.pathname;
      window.history.replaceState(null, '', newUrl);
    }
  }, []);

  async function handleSaveOpenRouterKey() {
    try {
      setSaving(true);
      setMessage(null);
      const { orgId, userId } = await ensureUserOrg({});
      await saveSecret({
        orgId,
        userId: undefined,
        purpose: "openrouter.key",
        secret: openRouterKey,
        createdBy: userId,
      });
      setMessage("✅ OpenRouter key saved successfully!");
      setOpenRouterKey("");
    } catch (e: any) {
      setMessage(`❌ Failed to save OpenRouter key: ${e?.message ?? "unknown error"}`);
    } finally {
      setSaving(false);
    }
  }

  async function handleConnectBot() {
    try {
      setSaving(true);
      setMessage(null);
      const { orgId, userId } = await ensureUserOrg({});
      await connectBot({ orgId, createdBy: userId, botToken, webhookSecret });
      setMessage("✅ Telegram bot connected successfully!");
      setBotToken("");
      setWebhookSecret("");
    } catch (e: any) {
      setMessage(`❌ Failed to connect Telegram bot: ${e?.message ?? "unknown error"}`);
    } finally {
      setSaving(false);
    }
  }

  async function handleDisconnect(provider: string) {
    try {
      setSaving(true);
      setMessage(null);
      await disconnectIntegration({ provider });
      setMessage(`✅ ${provider} disconnected successfully!`);
    } catch (e: any) {
      setMessage(`❌ Failed to disconnect ${provider}: ${e?.message ?? "unknown error"}`);
    } finally {
      setSaving(false);
    }
  }

  async function handleLinkTelegram() {
    if (!botUsername) {
      setMessage("❌ Please connect a Telegram bot first");
      return;
    }

    try {
      setLinkingInProgress(true);
      setMessage(null);
      const { nonce } = await generateNonce();
      
      // Create the deep link URL
      const linkUrl = `https://t.me/${botUsername}?start=${nonce}`;
      
      // Open the link
      window.open(linkUrl, '_blank');
      setMessage("📱 Please complete the linking process in Telegram. The link will expire in 10 minutes.");
    } catch (e: any) {
      setMessage(`❌ Failed to generate linking code: ${e?.message ?? "unknown error"}`);
    } finally {
      setLinkingInProgress(false);
    }
  }

  function StatusIndicator({ connected, provider }: { connected: boolean; provider: string }) {
    return (
      <div className="flex items-center gap-2">
        {connected ? (
          <>
            <CheckCircle2 className="h-4 w-4 text-primary" />
            <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
              Connected
            </Badge>
          </>
        ) : (
          <>
            <XCircle className="h-4 w-4 text-muted-foreground" />
            <Badge variant="outline" className="text-muted-foreground">
              Not Connected
            </Badge>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Integrations</h1>
          <p className="text-sm text-muted-foreground">Connect your tools to automate task creation</p>
        </div>
        {connectionStatuses && (
          <div className="text-sm text-muted-foreground">
            {Object.values(connectionStatuses).filter(Boolean).length} of 3 connected
          </div>
        )}
      </div>

      {message && (
        <Alert className={message.includes("❌") ? "border-destructive/20 bg-destructive/5" : "border-primary/20 bg-primary/5"}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Linear Integration */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>Linear</CardTitle>
            <StatusIndicator connected={connectionStatuses?.linear ?? false} provider="linear" />
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Automatically create Linear issues from Telegram messages
            </p>
            
            {connectionStatuses?.linear ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2 p-2 bg-primary/5 rounded-md border border-primary/20">
                  <CheckCircle2 className="h-4 w-4 text-primary" />
                  <span className="text-sm text-foreground font-medium">OAuth token active</span>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDisconnect("linear")}
                    disabled={saving}
                  >
                    Disconnect
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open("https://linear.app", "_blank")}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Open Linear
                  </Button>
                </div>
              </div>
            ) : (
              <a href="/api/oauth/linear/start">
                <Button className="w-full" disabled={saving}>
                  {saving ? "Connecting..." : "Connect Linear"}
                </Button>
              </a>
            )}
          </CardContent>
        </Card>

        {/* OpenRouter Integration */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>OpenRouter</CardTitle>
            <StatusIndicator connected={connectionStatuses?.openrouter ?? false} provider="openrouter" />
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              AI-powered message processing and task extraction
            </p>
            
            {connectionStatuses?.openrouter ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2 p-2 bg-primary/5 rounded-md border border-primary/20">
                  <CheckCircle2 className="h-4 w-4 text-primary" />
                  <span className="text-sm text-foreground font-medium">API key configured</span>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDisconnect("openrouter")}
                    disabled={saving}
                  >
                    Remove Key
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open("https://openrouter.ai/keys", "_blank")}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Manage Keys
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <Input
                  type="password"
                  placeholder="sk-or-v1-..."
                  value={openRouterKey}
                  onChange={(e) => setOpenRouterKey(e.target.value)}
                />
                <Button 
                  className="w-full"
                  disabled={saving || !openRouterKey} 
                  onClick={handleSaveOpenRouterKey}
                >
                  {saving ? "Saving..." : "Save API Key"}
                </Button>
                <p className="text-xs text-muted-foreground">
                  Get your API key from{" "}
                  <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                    openrouter.ai/keys
                  </a>
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Telegram Integration */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>Telegram</CardTitle>
            <StatusIndicator connected={connectionStatuses?.telegram ?? false} provider="telegram" />
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Receive and process messages from your Telegram bot
            </p>
            
            {connectionStatuses?.telegram ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2 p-2 bg-primary/5 rounded-md border border-primary/20">
                  <CheckCircle2 className="h-4 w-4 text-primary" />
                  <span className="text-sm text-foreground font-medium">Bot configured and active</span>
                </div>
                
                {/* Account Linking Section */}
                <div className="border-t pt-4">
                  <h4 className="text-sm font-medium mb-2">Account Linking</h4>
                  {telegramLinkingStatus ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 p-2 bg-primary/5 rounded-md border border-primary/20">
                        <CheckCircle2 className="h-4 w-4 text-primary" />
                        <span className="text-sm text-foreground font-medium">
                          Linked to {telegramLinkingStatus.firstName} {telegramLinkingStatus.lastName}
                          {telegramLinkingStatus.username && ` (@${telegramLinkingStatus.username})`}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Linked {new Date(telegramLinkingStatus.linkedAt).toLocaleDateString()}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">
                        Link your personal Telegram account to send tasks directly to the bot.
                      </p>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={handleLinkTelegram}
                        disabled={linkingInProgress || !botUsername}
                      >
                        {linkingInProgress ? "Generating Link..." : "Link My Account"}
                      </Button>
                    </div>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDisconnect("telegram")}
                    disabled={saving}
                  >
                    Disconnect Bot
                  </Button>
                  {botUsername && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.open(`https://t.me/${botUsername}`, "_blank")}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      Open Bot
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="botToken">Bot Token</Label>
                  <Input 
                    id="botToken" 
                    type="password" 
                    placeholder="123456:ABC-DEF..." 
                    value={botToken} 
                    onChange={(e) => setBotToken(e.target.value)} 
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="webhookSecret">Webhook Secret</Label>
                  <Input 
                    id="webhookSecret" 
                    type="password" 
                    placeholder="random-shared-secret" 
                    value={webhookSecret} 
                    onChange={(e) => setWebhookSecret(e.target.value)} 
                  />
                </div>
                <Button 
                  className="w-full"
                  disabled={saving || !botToken || !webhookSecret} 
                  onClick={handleConnectBot}
                >
                  {saving ? "Connecting..." : "Connect Bot"}
                </Button>
                <p className="text-xs text-muted-foreground">
                  Create a bot with{" "}
                  <a href="https://t.me/BotFather" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                    @BotFather
                  </a>{" "}
                  on Telegram
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
