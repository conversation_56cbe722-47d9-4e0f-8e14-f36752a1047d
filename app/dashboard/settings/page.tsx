"use client";

import { useState, useEffect } from "react";
import * as React from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Setting<PERSON>, 
  Users, 
  Crown,
  UserPlus,
  Trash2,
  Save,
  AlertCircle,
  Building2,
  CheckCircle2
} from "lucide-react";

export default function SettingsPage() {
  const [message, setMessage] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [orgSettings, setOrgSettings] = useState({
    name: "",
    defaultModel: "",
    defaultLinearTeamId: "",
    allowUserBYOKeys: true,
  });

  // Data queries
  const currentUser = useQuery(api.queries.getCurrentUser);
  const organizationData = useQuery(api.queries.getOrganizationData);
  const organizationMembers = useQuery(api.queries.getOrganizationMembers);

  // Mutations
  const updateOrganization = useMutation(api.orgs.updateOrganization);
  const updateUserRole = useMutation(api.orgs.updateUserRole);
  const removeUserFromOrg = useMutation(api.orgs.removeUserFromOrg);

  // Initialize form data when organization data loads
  React.useEffect(() => {
    if (organizationData) {
      setOrgSettings({
        name: organizationData.name,
        defaultModel: organizationData.settings.defaultModel || "",
        defaultLinearTeamId: organizationData.settings.defaultLinearTeamId || "",
        allowUserBYOKeys: organizationData.settings.allowUserBYOKeys ?? true,
      });
    }
  }, [organizationData]);

  const handleSaveOrganization = async () => {
    try {
      setSaving(true);
      setMessage(null);
      
      await updateOrganization({
        name: orgSettings.name,
        settings: {
          defaultModel: orgSettings.defaultModel || undefined,
          defaultLinearTeamId: orgSettings.defaultLinearTeamId || undefined,
          allowUserBYOKeys: orgSettings.allowUserBYOKeys,
        },
      });
      
      setMessage("✅ Organization settings saved successfully!");
    } catch (error: any) {
      setMessage(`❌ Failed to save settings: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateUserRole = async (userId: string, newRole: string) => {
    try {
      await updateUserRole({ userId, role: newRole });
      setMessage(`✅ User role updated successfully!`);
    } catch (error: any) {
      setMessage(`❌ Failed to update role: ${error.message}`);
    }
  };

  const handleRemoveUser = async (userId: string, userName: string) => {
    if (!confirm(`Are you sure you want to remove ${userName} from the organization?`)) {
      return;
    }
    
    try {
      await removeUserFromOrg({ userId });
      setMessage(`✅ User removed successfully!`);
    } catch (error: any) {
      setMessage(`❌ Failed to remove user: ${error.message}`);
    }
  };

  const getRoleColor = (role?: string) => {
    switch (role) {
      case "owner": return "bg-purple-100 text-purple-800 border-purple-200";
      case "admin": return "bg-blue-100 text-blue-800 border-blue-200";
      case "member": return "bg-gray-100 text-gray-800 border-gray-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const canManageUsers = currentUser?.role === "owner" || currentUser?.role === "admin";
  const canManageOrgSettings = currentUser?.role === "owner";

  if (!organizationData || !currentUser) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-48"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Organization Settings</h1>
          <p className="text-sm text-muted-foreground">
            Manage your organization and team settings
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          <Building2 className="h-3 w-3" />
          {organizationData.plan}
        </Badge>
      </div>

      {message && (
        <Alert className={message.includes("❌") ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="organization" className="space-y-4">
        <TabsList>
          <TabsTrigger value="organization" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Organization
          </TabsTrigger>
          <TabsTrigger value="members" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Members
          </TabsTrigger>
        </TabsList>

        <TabsContent value="organization">
          <Card>
            <CardHeader>
              <CardTitle>Organization Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <Label htmlFor="orgName">Organization Name</Label>
                <Input
                  id="orgName"
                  value={orgSettings.name}
                  onChange={(e) => setOrgSettings(prev => ({ ...prev, name: e.target.value }))}
                  disabled={!canManageOrgSettings}
                />
              </div>

              <Separator />

              <div className="space-y-3">
                <h3 className="font-medium">Default Settings</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label htmlFor="defaultModel">Default AI Model</Label>
                    <Select
                      value={orgSettings.defaultModel}
                      onValueChange={(value) => setOrgSettings(prev => ({ ...prev, defaultModel: value }))}
                      disabled={!canManageOrgSettings}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select default model" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="openrouter/auto">OpenRouter Auto</SelectItem>
                        <SelectItem value="gpt-4">GPT-4</SelectItem>
                        <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                        <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                        <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultTeam">Default Linear Team ID</Label>
                    <Input
                      id="defaultTeam"
                      placeholder="e.g., team_abc123"
                      value={orgSettings.defaultLinearTeamId}
                      onChange={(e) => setOrgSettings(prev => ({ ...prev, defaultLinearTeamId: e.target.value }))}
                      disabled={!canManageOrgSettings}
                    />
                    <p className="text-xs text-muted-foreground">
                      Default Linear team for creating issues
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="allowBYOKeys"
                    checked={orgSettings.allowUserBYOKeys}
                    onCheckedChange={(checked) => setOrgSettings(prev => ({ ...prev, allowUserBYOKeys: checked }))}
                    disabled={!canManageOrgSettings}
                  />
                  <Label htmlFor="allowBYOKeys">Allow users to bring their own API keys</Label>
                </div>
              </div>

              {canManageOrgSettings && (
                <div className="flex justify-end pt-2">
                  <Button onClick={handleSaveOrganization} disabled={saving}>
                    <Save className="h-4 w-4 mr-2" />
                    {saving ? "Saving..." : "Save Settings"}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Organization Members</CardTitle>
                <Button disabled={!canManageUsers}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite Member
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {organizationMembers && organizationMembers.length > 0 ? (
                organizationMembers.map((member) => (
                  <div key={member._id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="text-xs">
                          {member.name.split(" ").map(n => n[0]).join("").slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{member.name}</span>
                          {member.role === "owner" && <Crown className="h-3 w-3 text-yellow-500" />}
                        </div>
                        {member.email && (
                          <span className="text-xs text-muted-foreground">{member.email}</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={`text-xs ${getRoleColor(member.role)}`}>
                        {member.role || "member"}
                      </Badge>
                      
                      {canManageUsers && member._id !== currentUser._id && member.role !== "owner" && (
                        <div className="flex items-center gap-1">
                          <Select
                            value={member.role || "member"}
                            onValueChange={(newRole) => handleUpdateUserRole(member._id, newRole)}
                          >
                            <SelectTrigger className="w-24 h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="member">Member</SelectItem>
                              <SelectItem value="admin">Admin</SelectItem>
                              {currentUser.role === "owner" && (
                                <SelectItem value="owner">Owner</SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveUser(member._id, member.name)}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-muted-foreground py-4 text-sm">
                  No members found
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}