# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
BuddyTasks Pro - An AI-powered task management application built with Next.js 15, featuring Telegram bot integration, Linear issue creation via OpenRouter LLM processing, real-time database (Convex), authentication (Clerk), and subscription billing.

## Development Commands

### Core Development
- `npm run dev` - Start development server with Turbopack on http://localhost:3000
- `npm run build` - Build production bundle
- `npm start` - Start production server
- `npm run lint` - Run Next.js linting

### Convex Development
- `npx convex dev` - Start Convex development server (required for database)
- Run this in a separate terminal alongside `npm run dev`

### Package Management
- Uses npm for dependency management (package-lock.json present)
- Also supports bun (bun.lock present for faster installs)

## Architecture Overview

### Tech Stack
- **Next.js 15** with App Router and Turbopack
- **Convex** for real-time database and serverless functions
- **Clerk** for authentication and user management
- **Clerk Billing** for subscription payments
- **TailwindCSS v4** with custom UI components (shadcn/ui)
- **TypeScript** throughout
- **Drag & Drop**: @dnd-kit for sortable lists and interactive UI
- **Charts**: Recharts for data visualization
- **Animations**: Framer Motion for smooth transitions
- **Icons**: Tabler Icons and Lucide React

### Key Architectural Patterns

#### Authentication Flow
1. Clerk handles all authentication via `middleware.ts:3` - protects `/dashboard(.*)` routes
2. JWT tokens configured with "convex" template in Clerk dashboard
3. Users synced to Convex via webhooks handled by `convex/http.ts:11` at `/clerk-users-webhook`
4. Protected routes automatically redirect unauthenticated users

#### Database Architecture (Convex)
- **Real-time**: All data syncs automatically across clients
- **Schema** in `convex/schema.ts:5`:
  - `users` table: Synced from Clerk (`externalId` = Clerk ID)
  - `paymentAttempts` table: Tracks Clerk Billing subscription payments
- **HTTP Actions**: External webhooks handled at `convex/http.ts`
- **Serverless Functions**: Queries, mutations, and actions for all data operations
- **Envelope Encryption**: Sensitive data encrypted at application level per Convex security guidelines

#### Convex Function Patterns (Critical)
- **ALWAYS** use new function syntax with explicit args/returns validators
- **HTTP Actions** for external webhooks (Telegram, Linear, Clerk)
- **Actions** for external API calls (OpenRouter, Linear GraphQL, Telegram Bot API)
- **Internal functions** for sensitive operations (use `internalQuery`, `internalMutation`, `internalAction`)
- **Scheduling** via `ctx.scheduler` for decoupled webhook processing

#### Payment Integration
1. Clerk Billing handles subscription management
2. Custom pricing component in `components/custom-clerk-pricing.tsx`
3. Payment status tracked via webhook events in `convex/paymentAttempts.ts`
4. Subscription gating enforced in UI components

#### AI Task Processing Flow
1. Telegram bot receives message → webhook to `convex/http.ts`
2. Message queued and processed asynchronously via scheduler
3. OpenRouter LLM processes message content → structured task data
4. Linear GraphQL API creates issues with parsed data
5. Confirmation sent back to Telegram chat

### Project Structure
```
app/
├── (landing)/         # Public marketing pages
├── dashboard/         # Protected dashboard area
│   ├── payment-gated/ # Subscription-only features
│   ├── app-sidebar.tsx # Main navigation sidebar
│   └── data-table.tsx # Interactive data tables
├── layout.tsx         # Root layout with theme/auth providers
└── middleware.ts      # Clerk route protection

components/
├── ui/               # shadcn/ui base components
├── custom-clerk-pricing.tsx # Billing integration
├── ConvexClientProvider.tsx  # Convex real-time provider
├── kokonutui/        # Custom animated components
├── magicui/          # Advanced UI components
└── motion-primitives/ # Animation utilities

convex/
├── schema.ts         # Database schema with indexes
├── users.ts          # User CRUD operations
├── paymentAttempts.ts # Billing webhook handling
├── http.ts           # External webhook endpoints
└── auth.config.ts    # JWT/Clerk configuration
```

## Key Integration Points

### Environment Variables Required
- `CONVEX_DEPLOYMENT` and `NEXT_PUBLIC_CONVEX_URL` (from Convex dashboard)
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY`
- `NEXT_PUBLIC_CLERK_FRONTEND_API_URL` (from Clerk JWT template)
- `CLERK_WEBHOOK_SECRET` (set in Convex environment variables)

### Webhook Configuration
- **Clerk webhooks** → `https://your-app.convex.site/clerk-users-webhook`
- **Events**: `user.created`, `user.updated`, `user.deleted`, `paymentAttempt.updated`
- **Security**: All webhooks verify signatures using `svix` library
- **Processing**: Webhook responses are immediate (200 OK), actual processing is scheduled

### Real-time Data Flow
1. UI components use Convex hooks (`useQuery`, `useMutation`, `useAction`)
2. Convex provides automatic real-time updates across all clients
3. Authentication context from Clerk's `useAuth()`
4. All data operations go through Convex functions (no direct DB access from frontend)

## Development Guidelines

### Convex Function Rules (Critical)
- **Always** include explicit `args` and `returns` validators
- Use `v.null()` for functions that don't return data
- **Internal functions** (`internalQuery`, `internalMutation`, `internalAction`) for sensitive operations
- **Public functions** (`query`, `mutation`, `action`) for client-accessible APIs
- Use `ctx.scheduler.runAfter()` to decouple webhook responses from processing
- Define indexes in schema for query performance (avoid `.filter()`)

### Security Requirements
- **Never** store plaintext secrets in database or logs
- Use envelope encryption for user API keys and tokens
- Validate all webhook signatures (Clerk, Telegram, Linear)
- Rate limit external API calls
- Sanitize/truncate logged message content

### UI Component Patterns
- **Theme Support**: All components support light/dark mode
- **Real-time Updates**: Use Convex hooks for live data
- **Loading States**: Implement proper skeleton loaders
- **Error Boundaries**: Handle API failures gracefully
- **Drag & Drop**: Use @dnd-kit for sortable interfaces

### API Integration Patterns
- **Telegram Bot**: Webhook verification with secret token
- **OpenRouter**: Include HTTP-Referer and X-Title headers
- **Linear**: OAuth flow for token management, GraphQL for issue creation
- **Clerk Billing**: Handle subscription webhooks for access control

## Component Installation Rules

### shadcn/ui Components
When installing shadcn/ui components:
- ALWAYS use `bunx --bun shadcn@latest add [component-name]`
- Check `components.json:12` for existing configuration
- Multiple components: `bunx --bun shadcn@latest add button card drawer`
- If dependency fails, manually run `bun install [dependency-name]`

### Package Management
- Primary: npm (package-lock.json)
- Fast installs: bun (bun.lock present)
- Always verify dependencies in package.json after component installation

## AI Task Processing Architecture

This application's core feature transforms Telegram messages into Linear tasks:

### Flow Overview
1. **Input**: Telegram bot receives message
2. **Processing**: OpenRouter LLM parses content → structured task data
3. **Output**: Linear issue created + Telegram confirmation

### Key Components
- **Telegram Integration**: Bot webhook handling, message routing
- **AI Processing**: OpenRouter API with structured output prompts
- **Task Management**: Linear OAuth, GraphQL issue creation
- **User Management**: Multi-tenant with encrypted credentials
- **Billing**: Subscription-based feature gating

This architecture enables users to quickly create well-structured tasks from natural language input across team collaboration tools.
