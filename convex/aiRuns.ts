import { internalMutation } from "./_generated/server";
import { v } from "convex/values";

export const create = internalMutation({
  args: {
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    inboundMessageId: v.optional(v.id("inboundMessages")),
    model: v.string(),
    promptPreview: v.string(),
    responsePreview: v.string(),
    usage: v.any(),
    status: v.string(),
    error: v.optional(v.string()),
    createdAt: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("aiRuns", args as any);
  },
});