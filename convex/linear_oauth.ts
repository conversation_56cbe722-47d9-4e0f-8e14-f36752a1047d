import { action } from "./_generated/server";
import { v } from "convex/values";
import { internal, api } from "./_generated/api";

export const exchangeCode = action({
  args: {
    code: v.string(),
    redirectUri: v.string(),
    orgId: v.id("orgs"),
    userId: v.id("users"),
  },
  handler: async (ctx, { code, redirectUri, orgId, userId }) => {
    const clientId = process.env.LINEAR_CLIENT_ID;
    const clientSecret = process.env.LINEAR_CLIENT_SECRET;
    if (!clientId || !clientSecret) throw new Error("Missing Linear OAuth env vars");

    const body = new URLSearchParams({
      grant_type: "authorization_code",
      code,
      redirect_uri: redirectUri,
      client_id: clientId,
      client_secret: clientSecret,
    });

    const res = await fetch("https://api.linear.app/oauth/token", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body,
    });
    if (!res.ok) {
      const t = await res.text();
      throw new Error(`Linear token exchange failed: ${res.status} ${t}`);
    }
    const data = await res.json();
    const accessToken = data.access_token as string;

    // Save token as secret
    const secretResult = await ctx.runAction(api.secrets.saveSecret, {
      orgId,
      userId: undefined,
      purpose: "linear.oauth",
      secret: accessToken,
      createdBy: userId,
    });

    // Record connection
    await ctx.runMutation(internal.connections.upsertConnection, {
      orgId,
      userId,
      provider: "linear",
      type: "oauth",
      status: "connected",
      displayName: "Linear OAuth",
      metadata: {},
      secretRefId: secretResult.secretId,
    });

    return { ok: true };
  },
});
