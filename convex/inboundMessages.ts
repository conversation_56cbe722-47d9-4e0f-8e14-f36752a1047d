import { internalMutation } from "./_generated/server";
import { v } from "convex/values";

export const create = internalMutation({
  args: {
    orgId: v.id("orgs"),
    chatId: v.string(),
    telegramUpdateId: v.string(),
    fromTelegramUserId: v.string(),
    text: v.string(),
    raw: v.optional(v.object({})),
    status: v.string(),
    error: v.optional(v.string()),
    processedAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("inboundMessages", args as any);
  },
});
