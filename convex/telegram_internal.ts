import { internalMutation } from "./_generated/server";
import { v } from "convex/values";

export const createOrUpdateBot = internalMutation({
  args: {
    orgId: v.id("orgs"),
    username: v.optional(v.string()),
    name: v.optional(v.string()),
    webhookSecret: v.string(),
    secretRefId: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if bot exists for this org
    const existing = await ctx.db
      .query("telegramBots")
      .withIndex("by_org", (q) => q.eq("orgId", args.orgId))
      .first();
    
    if (existing) {
      await ctx.db.patch(existing._id, {
        ...args,
        status: "active",
        updatedAt: Date.now(),
      } as any);
      return existing._id;
    }
    
    return await ctx.db.insert("telegramBots", {
      ...args,
      status: "active",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    } as any);
  },
});