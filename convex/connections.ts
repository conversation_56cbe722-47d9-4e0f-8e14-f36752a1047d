import { internalMutation, mutation } from "./_generated/server";
import { v } from "convex/values";

async function userByExternalId(ctx: any, externalId: string) {
  return await ctx.db
    .query("users")
    .withIndex("byExternalId", (q: any) => q.eq("externalId", externalId))
    .unique();
}

export const upsertConnection = internalMutation({
  args: {
    orgId: v.id("orgs"),
    userId: v.id("users"),
    provider: v.string(),
    type: v.optional(v.string()),
    status: v.string(),
    displayName: v.optional(v.string()),
    metadata: v.optional(v.object({})),
    secretRefId: v.optional(v.id("secrets")),
  },
  handler: async (ctx, values) => {
    // Upsert by org+provider+type
    const existing = await ctx.db
      .query("connections")
      .withIndex("by_org", (q) => q.eq("orgId", values.orgId))
      .filter((q) => q.eq(q.field("provider"), values.provider))
      .collect();
    const found = existing.find((c) => c.type === values.type);
    if (found) {
      await ctx.db.patch(found._id, values as any);
      return { id: found._id };
    }
    const id = await ctx.db.insert("connections", values as any);
    return { id };
  },
});

export const disconnectIntegration = mutation({
  args: {
    provider: v.string(),
  },
  handler: async (ctx, { provider }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");
    
    // Mark connection as revoked
    const connection = await ctx.db
      .query("connections")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
      .filter((q) => q.eq(q.field("provider"), provider))
      .first();
    
    if (connection) {
      await ctx.db.patch(connection._id, { status: "revoked" });
    }
    
    // Delete associated secrets
    if (provider === "openrouter") {
      const secret = await ctx.db
        .query("secrets")
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
        .filter((q) => q.eq(q.field("purpose"), "openrouter.key"))
        .first();
      if (secret) {
        await ctx.db.delete(secret._id);
      }
    }
    
    if (provider === "linear") {
      const secret = await ctx.db
        .query("secrets")
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
        .filter((q) => q.eq(q.field("purpose"), "linear.oauth"))
        .first();
      if (secret) {
        await ctx.db.delete(secret._id);
      }
    }
    
    if (provider === "telegram") {
      // Mark bot as disabled
      const bot = await ctx.db
        .query("telegramBots")
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
        .first();
      if (bot) {
        await ctx.db.patch(bot._id, { status: "disabled" });
      }
      
      // Delete bot token secret
      const secret = await ctx.db
        .query("secrets")
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
        .filter((q) => q.eq(q.field("purpose"), "telegram.bot_token"))
        .first();
      if (secret) {
        await ctx.db.delete(secret._id);
      }

      // Remove Telegram account linking
      const telegramAccount = await ctx.db
        .query("telegramAccounts")
        .withIndex("by_user", (q) => q.eq("userId", user._id))
        .first();
      if (telegramAccount) {
        await ctx.db.delete(telegramAccount._id);
      }
    }
    
    return { success: true };
  },
});

export const generateTelegramLinkingNonce = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");

    // Generate a secure random nonce
    const nonce = crypto.randomUUID();
    
    // Store nonce temporarily (expires in 10 minutes)
    const expiresAt = Date.now() + 10 * 60 * 1000;
    
    await ctx.db.insert("telegramLinkingNonces", {
      nonce,
      userId: user._id,
      orgId: user.orgId,
      expiresAt,
      used: false,
    });
    
    return { nonce };
  },
});

export const linkTelegramAccount = mutation({
  args: {
    telegramUserId: v.string(),
    username: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, { telegramUserId, username, firstName, lastName }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");

    // Check if this Telegram account is already linked to another user
    const existingAccount = await ctx.db
      .query("telegramAccounts")
      .withIndex("by_telegram_user", (q) => q.eq("telegramUserId", telegramUserId))
      .first();
    
    if (existingAccount && existingAccount.userId !== user._id) {
      throw new Error("This Telegram account is already linked to another user");
    }

    // Create or update the Telegram account link
    if (existingAccount) {
      await ctx.db.patch(existingAccount._id, {
        username,
        firstName,
        lastName,
        linkedAt: Date.now(),
      });
      return { success: true, accountId: existingAccount._id };
    } else {
      const accountId = await ctx.db.insert("telegramAccounts", {
        orgId: user.orgId,
        userId: user._id,
        telegramUserId,
        username,
        firstName,
        lastName,
        linkedAt: Date.now(),
      });
      return { success: true, accountId };
    }
  },
});

