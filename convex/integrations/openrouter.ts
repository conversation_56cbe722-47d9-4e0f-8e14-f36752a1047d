import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal, api } from "../_generated/api";

export const extractTaskFromMessage = action({
  args: {
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    inboundMessageId: v.optional(v.id("inboundMessages")),
    message: v.string(),
    context: v.optional(v.object({
      chatTitle: v.optional(v.string()),
      senderName: v.optional(v.string()),
      linearTeamId: v.optional(v.string()),
      defaultLabels: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, { orgId, userId, inboundMessageId, message, context }) => {
    const now = Date.now();
    
    // Get organization settings for default model
    const org = await ctx.runQuery(internal.orgs.getById, { orgId });
    const model = org?.settings?.defaultModel ?? "google/gemini-2.0-flash-exp";

    // Try to get OpenRouter key from secrets
    const secretDoc = await ctx.runQuery(internal.secrets.findSecret, {
      orgId,
      purpose: "openrouter.key",
      userId: undefined,
    });
    
    let secret: string | null = null;
    if (secretDoc) {
      const base64 = process.env.APP_ENCRYPTION_KEY;
      if (base64) {
        const raw = Buffer.from(base64, "base64");
        const key = await crypto.subtle.importKey(
          "raw",
          raw,
          { name: "AES-GCM", length: 256 },
          false,
          ["decrypt"],
        );
        const plaintextBuf = await crypto.subtle.decrypt(
          { name: "AES-GCM", iv: secretDoc.iv },
          key,
          secretDoc.ciphertext,
        );
        secret = new TextDecoder().decode(plaintextBuf);
      }
    }

    // Fallback if no OpenRouter key - basic parsing
    if (!secret) {
      const title = message.split("\n")[0].slice(0, 80) || "New Task";
      const description = message;
      const labels: string[] = context?.defaultLabels ?? [];
      
      await ctx.runMutation(internal.aiRuns.create, {
        orgId,
        userId,
        inboundMessageId,
        model: "fallback",
        promptPreview: message.slice(0, 200),
        responsePreview: title,
        usage: { prompt_tokens: message.length / 4, completion_tokens: 24, total_tokens: message.length / 4 + 24 } as any,
        status: "succeeded",
        error: "No OpenRouter key - using fallback parsing",
        createdAt: now,
      });
      
      return { 
        tasks: [{ 
          title, 
          description, 
          labels,
          teamId: context?.linearTeamId,
          priority: "normal" as const,
        }]
      };
    }

    // Enhanced system prompt for better task extraction
    const systemPrompt = `You are an AI assistant that extracts actionable tasks from messages and converts them into Linear-ready issues.

Context:
${context?.chatTitle ? `- Chat: ${context.chatTitle}` : ""}
${context?.senderName ? `- From: ${context.senderName}` : ""}
${context?.linearTeamId ? `- Team ID: ${context.linearTeamId}` : ""}
${context?.defaultLabels?.length ? `- Default Labels: ${context.defaultLabels.join(", ")}` : ""}

Instructions:
1. Extract actionable tasks from the message
2. Create clear, specific titles (max 80 chars)
3. Provide detailed descriptions with context
4. Suggest relevant labels (bug, feature, urgent, etc.)
5. Determine priority: low, normal, high, urgent
6. If multiple tasks exist, separate them
7. If the message is not task-related, return an empty tasks array

Response format: JSON array of task objects with: title, description, labels, priority`;

    // Attempt real OpenRouter call with enhanced prompting
    try {
      const res = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${secret}`,
          "X-Title": "BuddyTasks Pro",
          "HTTP-Referer": "https://buddytasks.pro",
        },
        body: JSON.stringify({
          model,
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: message },
          ],
          response_format: { 
            type: "json_schema", 
            json_schema: { 
              name: "task_extraction",
              schema: { 
                type: "object",
                properties: {
                  tasks: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        title: { type: "string", maxLength: 80 },
                        description: { type: "string" },
                        labels: { type: "array", items: { type: "string" } },
                        priority: { 
                          type: "string",
                          enum: ["low", "normal", "high", "urgent"]
                        }
                      },
                      required: ["title", "description", "priority"]
                    }
                  }
                },
                required: ["tasks"]
              }
            }
          },
          temperature: 0.3, // Lower temperature for more consistent parsing
          max_tokens: 2000,
        }),
      });

      if (!res.ok) {
        throw new Error(`OpenRouter API error: ${res.status} ${res.statusText}`);
      }

      const json = await res.json();
      const content = json?.choices?.[0]?.message?.content;
      
      let parsed: any = {};
      try { 
        parsed = typeof content === "string" ? JSON.parse(content) : content; 
      } catch (parseError) {
        throw new Error(`Failed to parse JSON response: ${parseError}`);
      }

      // Validate and enhance the parsed response
      const tasks = Array.isArray(parsed.tasks) ? parsed.tasks : [];
      const enhancedTasks = tasks.map((task: any) => ({
        title: task.title ?? (message.split("\n")[0].slice(0, 80) || "New Task"),
        description: task.description ?? message,
        labels: Array.isArray(task.labels) ? 
          [...(task.labels ?? []), ...(context?.defaultLabels ?? [])] : 
          (context?.defaultLabels ?? []),
        teamId: context?.linearTeamId,
        priority: ["low", "normal", "high", "urgent"].includes(task.priority) ? 
          task.priority : "normal",
      }));

      // If no tasks extracted but message seems actionable, create one
      if (enhancedTasks.length === 0 && message.length > 10) {
        enhancedTasks.push({
          title: message.split("\n")[0].slice(0, 80) || "New Task",
          description: message,
          labels: context?.defaultLabels ?? [],
          teamId: context?.linearTeamId,
          priority: "normal" as const,
        });
      }

      await ctx.runMutation(internal.aiRuns.create, {
        orgId,
        userId,
        inboundMessageId,
        model,
        promptPreview: message.slice(0, 200),
        responsePreview: typeof content === "string" ? content.slice(0, 200) : JSON.stringify(enhancedTasks).slice(0, 200),
        usage: json?.usage ?? undefined,
        status: "succeeded",
        error: undefined,
        createdAt: now,
      });

      return { tasks: enhancedTasks };
    } catch (e: any) {
      await ctx.runMutation(internal.aiRuns.create, {
        orgId,
        userId,
        inboundMessageId,
        model,
        promptPreview: message.slice(0, 200),
        responsePreview: e?.message ?? "",
        usage: undefined,
        status: "failed",
        error: String(e),
        createdAt: now,
      });

      // Return fallback task even on error
      const fallbackTitle = message.split("\n")[0].slice(0, 80) || "New Task";
      return { 
        tasks: [{ 
          title: fallbackTitle,
          description: message,
          labels: context?.defaultLabels ?? [],
          teamId: context?.linearTeamId,
          priority: "normal" as const,
        }]
      };
    }
  },
});

// Legacy function for backward compatibility
export const chatComplete: any = action({
  args: {
    orgId: v.id("orgs"),
    prompt: v.string(),
    model: v.string(),
  },
  handler: async (ctx, { orgId, prompt, model }): Promise<any> => {
    const result: any = await ctx.runAction((api as any)["integrations/openrouter"].extractTaskFromMessage, {
      orgId,
      message: prompt,
    });
    
    const firstTask: any = result.tasks[0];
    if (!firstTask) {
      return {
        title: "New Task",
        description: prompt,
        labels: [],
      };
    }

    return {
      title: firstTask.title,
      description: firstTask.description,
      labels: firstTask.labels,
    };
  },
});
