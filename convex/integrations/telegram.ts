import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal, api } from "../_generated/api";

export const sendMessage = action({
  args: { orgId: v.id("orgs"), chatId: v.string(), text: v.string() },
  handler: async (ctx, { orgId, chatId, text }) => {
    const msgId = await ctx.runMutation(internal.telegram.insertOutboundMessage, { orgId, chatId, text });
    try {
      const secretDoc = await ctx.runQuery(internal.secrets.findSecret, { orgId, purpose: "telegram.bot_token", userId: undefined });
      if (!secretDoc) throw new Error("Missing bot token");
      const base64 = process.env.APP_ENCRYPTION_KEY;
      if (!base64) throw new Error("Missing APP_ENCRYPTION_KEY");
      const raw = Buffer.from(base64, "base64");
      const key = await crypto.subtle.importKey("raw", raw, { name: "AES-GCM", length: 256 }, false, ["decrypt"]);
      const plaintextBuf = await crypto.subtle.decrypt({ name: "AES-GCM", iv: secretDoc.iv }, key, secretDoc.ciphertext);
      const token = new TextDecoder().decode(plaintextBuf);

      const res = await fetch(`https://api.telegram.org/bot${token}/sendMessage`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chat_id: chatId, text }),
      });
      const json = await res.json();
      const telegramMessageId = json?.result?.message_id ? String(json.result.message_id) : undefined;
      await ctx.runMutation(internal.telegram.markOutboundStatus, { id: msgId, status: "sent", error: undefined, telegramMessageId });
      return { ok: true };
    } catch (e: any) {
      await ctx.runMutation(internal.telegram.markOutboundStatus, { id: msgId, status: "error", error: String(e), telegramMessageId: undefined });
      return { ok: false, error: String(e) };
    }
  },
});

export const connectBot: any = action({
  args: {
    orgId: v.id("orgs"),
    createdBy: v.id("users"),
    botToken: v.string(),
    webhookSecret: v.string(),
  },
  handler: async (ctx, { orgId, createdBy, botToken, webhookSecret }): Promise<any> => {
    // Save bot token as secret
    const secretRes: any = await ctx.runAction(api.secrets.saveSecret, {
      orgId,
      userId: undefined,
      purpose: "telegram.bot_token",
      secret: botToken,
      createdBy,
    });

    // Create or update bot
    const botId: any = await ctx.runMutation(internal.telegram_internal.createOrUpdateBot, {
      orgId,
      username: undefined,
      name: undefined,
      webhookSecret,
      secretRefId: secretRes.secretId,
    });

    // Set webhook if possible
    const siteUrl = process.env.CONVEX_SITE_URL; // e.g., https://<deployment>.convex.site
    if (siteUrl) {
      const token = botToken;
      const webhookUrl = `${siteUrl}/telegram/webhook`;
      try {
        const res = await fetch(`https://api.telegram.org/bot${token}/setWebhook`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ url: webhookUrl, secret_token: webhookSecret }),
        });
        await res.json();
      } catch (e) {
        // Swallow; webhook can be retried in UI
      }
    }
    return { ok: true, botId };
  },
});
