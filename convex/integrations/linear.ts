import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal, api } from "../_generated/api";

export const createIssue: any = action({
  args: {
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    inboundMessageId: v.optional(v.id("inboundMessages")),
    title: v.string(),
    description: v.string(),
    teamId: v.optional(v.string()),
    labels: v.optional(v.array(v.string())),
    priority: v.optional(v.union(v.literal("low"), v.literal("normal"), v.literal("high"), v.literal("urgent"))),
  },
  handler: async (ctx, { orgId, userId, inboundMessageId, title, description, teamId, labels, priority }): Promise<any> => {
    // Get Linear token from secrets
    const tokenDoc = await ctx.runQuery(internal.secrets.findSecret, {
      orgId,
      purpose: "linear.oauth",
      userId: undefined,
    });
    
    let token: string | null = null;
    if (tokenDoc) {
      const base64 = process.env.APP_ENCRYPTION_KEY;
      if (base64) {
        try {
          const raw = Buffer.from(base64, "base64");
          const key = await crypto.subtle.importKey(
            "raw", 
            raw, 
            { name: "AES-GCM", length: 256 }, 
            false, 
            ["decrypt"]
          );
          const plaintextBuf = await crypto.subtle.decrypt(
            { name: "AES-GCM", iv: tokenDoc.iv }, 
            key, 
            tokenDoc.ciphertext
          );
          token = new TextDecoder().decode(plaintextBuf);
        } catch (decryptError) {
          console.error("Failed to decrypt Linear token:", decryptError);
        }
      }
    }

    // Fallback if no token - create stub issue
    if (!token) {
      const fakeId = `DEMO-${Math.floor(Math.random() * 10000)}`;
      const issueUrl = `https://linear.app/issue/${fakeId}`;
      
      // Still log the issue in our database for tracking
      const issueId: any = await ctx.runMutation(internal.issues.create, {
        orgId,
        inboundMessageId: inboundMessageId!,
        linearIssueId: fakeId,
        linearUrl: issueUrl,
        teamId,
        title,
        status: "created",
        error: "Demo mode - Linear not connected",
        createdAt: Date.now(),
      });

      return { 
        issueId: fakeId, 
        issueUrl, 
        error: "Demo mode - Connect Linear to create real issues",
        dbIssueId: issueId,
      };
    }

    // Get organization settings for default team
    const org = await ctx.runQuery(internal.orgs.getById, { orgId });
    const finalTeamId = teamId || org?.settings?.defaultLinearTeamId;

    // First, resolve label names to IDs if labels are provided
    let labelIds: string[] = [];
    if (labels && labels.length > 0) {
      try {
        const labelQuery = `
          query Labels($filter: LabelFilter) {
            labels(filter: $filter) {
              nodes {
                id
                name
              }
            }
          }
        `;

        const labelRes = await fetch("https://api.linear.app/graphql", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
          },
          body: JSON.stringify({
            query: labelQuery,
            variables: {
              filter: {
                name: { in: labels }
              }
            }
          }),
        });

        const labelJson = await labelRes.json();
        const foundLabels = labelJson?.data?.labels?.nodes ?? [];
        labelIds = foundLabels.map((label: any) => label.id);
      } catch (labelError) {
        console.warn("Failed to resolve label IDs:", labelError);
        // Continue without labels rather than failing the entire operation
      }
    }

    // Map priority to Linear priority values
    let linearPriority: number | undefined;
    switch (priority) {
      case "urgent": linearPriority = 1; break;
      case "high": linearPriority = 2; break;
      case "normal": linearPriority = 3; break;
      case "low": linearPriority = 4; break;
    }

    // Create the issue
    const createMutation = `
      mutation IssueCreate($input: IssueCreateInput!) {
        issueCreate(input: $input) {
          success
          issue {
            id
            identifier
            url
            title
            team {
              id
              name
            }
          }
          lastSyncId
        }
      }
    `;

    const variables = {
      input: {
        title: title.slice(0, 255), // Linear has title length limits
        description,
        teamId: finalTeamId,
        labelIds: labelIds.length > 0 ? labelIds : undefined,
        priority: linearPriority,
      },
    };

    try {
      const res = await fetch("https://api.linear.app/graphql", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({ query: createMutation, variables }),
      });

      if (!res.ok) {
        throw new Error(`Linear API HTTP ${res.status}: ${res.statusText}`);
      }

      const json = await res.json();
      
      if (json.errors) {
        throw new Error(`Linear GraphQL errors: ${JSON.stringify(json.errors)}`);
      }

      const issueData = json?.data?.issueCreate;
      if (!issueData?.success || !issueData?.issue) {
        throw new Error(`Issue creation failed: ${JSON.stringify(issueData)}`);
      }

      const issue = issueData.issue;
      const issueIdentifier = issue.identifier;
      const issueUrl = issue.url;
      const issueId = issue.id;

      // Save issue to our database for tracking
      let dbIssueId;
      if (inboundMessageId) {
        dbIssueId = await ctx.runMutation(internal.issues.create, {
          orgId,
          inboundMessageId,
          linearIssueId: issueIdentifier,
          linearUrl: issueUrl,
          teamId: issue.team?.id || finalTeamId,
          title: issue.title,
          status: "created",
          error: undefined,
          createdAt: Date.now(),
        });
      }

      return { 
        issueId: issueIdentifier,
        issueUrl,
        linearId: issueId,
        teamName: issue.team?.name,
        error: undefined,
        dbIssueId,
      };

    } catch (e: any) {
      console.error("Linear issue creation failed:", e);

      // Save error to database if we have an inbound message
      let dbIssueId;
      if (inboundMessageId) {
        dbIssueId = await ctx.runMutation(internal.issues.create, {
          orgId,
          inboundMessageId,
          linearIssueId: "",
          linearUrl: "",
          teamId: finalTeamId,
          title,
          status: "error",
          error: String(e),
          createdAt: Date.now(),
        });
      }

      return { 
        issueId: undefined, 
        issueUrl: undefined,
        linearId: undefined,
        teamName: undefined,
        error: String(e),
        dbIssueId,
      };
    }
  },
});

export const createIssuesFromTasks: any = action({
  args: {
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    inboundMessageId: v.optional(v.id("inboundMessages")),
    tasks: v.array(v.object({
      title: v.string(),
      description: v.string(),
      labels: v.optional(v.array(v.string())),
      teamId: v.optional(v.string()),
      priority: v.optional(v.union(v.literal("low"), v.literal("normal"), v.literal("high"), v.literal("urgent"))),
    })),
  },
  handler: async (ctx, { orgId, userId, inboundMessageId, tasks }): Promise<any> => {
    const results = [];
    
    for (const task of tasks) {
      const result: any = await ctx.runAction((api as any)["integrations/linear"].createIssue, {
        orgId,
        userId,
        inboundMessageId,
        title: task.title,
        description: task.description,
        teamId: task.teamId,
        labels: task.labels,
        priority: task.priority,
      });
      results.push(result);
    }

    return { issues: results };
  },
});

export const getTeams = action({
  args: {
    orgId: v.id("orgs"),
  },
  handler: async (ctx, { orgId }) => {
    const tokenDoc = await ctx.runQuery(internal.secrets.findSecret, {
      orgId,
      purpose: "linear.oauth",
      userId: undefined,
    });
    
    let token: string | null = null;
    if (tokenDoc) {
      const base64 = process.env.APP_ENCRYPTION_KEY;
      if (base64) {
        try {
          const raw = Buffer.from(base64, "base64");
          const key = await crypto.subtle.importKey(
            "raw", 
            raw, 
            { name: "AES-GCM", length: 256 }, 
            false, 
            ["decrypt"]
          );
          const plaintextBuf = await crypto.subtle.decrypt(
            { name: "AES-GCM", iv: tokenDoc.iv }, 
            key, 
            tokenDoc.ciphertext
          );
          token = new TextDecoder().decode(plaintextBuf);
        } catch (decryptError) {
          return { teams: [], error: "Failed to decrypt Linear token" };
        }
      }
    }

    if (!token) {
      return { teams: [], error: "Linear not connected" };
    }

    const teamsQuery = `
      query Teams {
        teams {
          nodes {
            id
            name
            key
            description
          }
        }
      }
    `;

    try {
      const res = await fetch("https://api.linear.app/graphql", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({ query: teamsQuery }),
      });

      const json = await res.json();
      
      if (json.errors) {
        throw new Error(`Linear GraphQL errors: ${JSON.stringify(json.errors)}`);
      }

      const teams = json?.data?.teams?.nodes ?? [];
      return { teams, error: undefined };
    } catch (e: any) {
      return { teams: [], error: String(e) };
    }
  },
});

// Legacy function for backward compatibility
export const createIssueFromTask: any = action({
  args: {
    orgId: v.id("orgs"),
    title: v.string(),
    description: v.string(),
    teamId: v.optional(v.string()),
    labels: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args): Promise<any> => {
    return await ctx.runAction((api as any)["integrations/linear"].createIssue, {
      ...args,
      priority: "normal",
    });
  },
});
