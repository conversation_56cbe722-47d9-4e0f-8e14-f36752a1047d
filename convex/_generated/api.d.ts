/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as aiRuns from "../aiRuns.js";
import type * as connections from "../connections.js";
import type * as http from "../http.js";
import type * as inboundMessages from "../inboundMessages.js";
import type * as integrations_linear from "../integrations/linear.js";
import type * as integrations_openrouter from "../integrations/openrouter.js";
import type * as integrations_telegram from "../integrations/telegram.js";
import type * as issues from "../issues.js";
import type * as linear_oauth from "../linear_oauth.js";
import type * as orgs from "../orgs.js";
import type * as paymentAttemptTypes from "../paymentAttemptTypes.js";
import type * as paymentAttempts from "../paymentAttempts.js";
import type * as queries from "../queries.js";
import type * as routing from "../routing.js";
import type * as secrets from "../secrets.js";
import type * as telegram from "../telegram.js";
import type * as telegramBots from "../telegramBots.js";
import type * as telegram_internal from "../telegram_internal.js";
import type * as users from "../users.js";
import type * as webhookEvents from "../webhookEvents.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  aiRuns: typeof aiRuns;
  connections: typeof connections;
  http: typeof http;
  inboundMessages: typeof inboundMessages;
  "integrations/linear": typeof integrations_linear;
  "integrations/openrouter": typeof integrations_openrouter;
  "integrations/telegram": typeof integrations_telegram;
  issues: typeof issues;
  linear_oauth: typeof linear_oauth;
  orgs: typeof orgs;
  paymentAttemptTypes: typeof paymentAttemptTypes;
  paymentAttempts: typeof paymentAttempts;
  queries: typeof queries;
  routing: typeof routing;
  secrets: typeof secrets;
  telegram: typeof telegram;
  telegramBots: typeof telegramBots;
  telegram_internal: typeof telegram_internal;
  users: typeof users;
  webhookEvents: typeof webhookEvents;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
