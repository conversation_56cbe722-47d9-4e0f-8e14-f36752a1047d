import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { paymentAttemptSchemaValidator } from "./paymentAttemptTypes";

export default defineSchema({
  // Core users (Clerk synced)
  users: defineTable({
    name: v.string(),
    // Clerk ID stored in the subject JWT field
    externalId: v.string(),
    orgId: v.optional(v.id("orgs")),
    email: v.optional(v.string()),
    role: v.optional(v.string()), // "owner" | "admin" | "member"
  })
    .index("byExternalId", ["externalId"]) // existing index
    .index("byOrg", ["orgId"]),

  // Organizations
  orgs: defineTable({
    name: v.string(),
    clerkOrganizationId: v.optional(v.string()),
    plan: v.string(), // "free" | "pro" | "enterprise"
    settings: v.object({
      defaultModel: v.optional(v.string()),
      defaultLinearTeamId: v.optional(v.string()),
      allowUserBYOKeys: v.optional(v.boolean()),
    }),
  }).index("by_clerk_org", ["clerkOrganizationId"]),

  // Subscriptions (synced from Clerk Billing)
  subscriptions: defineTable({
    orgId: v.id("orgs"),
    provider: v.literal("clerk"),
    externalId: v.string(), // Clerk subscription id
    status: v.string(), // "active" | "trialing" | "past_due" | "canceled" ...
    plan: v.string(),
    currentPeriodEnd: v.number(), // ms epoch
  }).index("by_org", ["orgId"]),

  // Connections & Secrets
  connections: defineTable({
    orgId: v.id("orgs"),
    userId: v.id("users"), // who connected it
    provider: v.string(), // "linear" | "telegram" | "openrouter"
    type: v.optional(v.string()), // "oauth" | "api_key" | "bot"
    status: v.string(), // "connected" | "revoked" | "error" | "pending"
    displayName: v.optional(v.string()),
    metadata: v.optional(v.object({})),
    secretRefId: v.optional(v.id("secrets")),
  })
    .index("by_org", ["orgId"])
    .index("by_user", ["userId"])
    .index("by_provider", ["provider"]),

  secrets: defineTable({
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    purpose: v.string(), // e.g., "linear.oauth", "openrouter.key", "telegram.bot_token"
    algorithm: v.string(), // e.g., "AES-256-GCM"
    ciphertext: v.bytes(),
    iv: v.bytes(),
    tag: v.bytes(),
    keyVersion: v.number(),
    wrappedKey: v.bytes(),
    wrapping: v.object({ method: v.string(), keyId: v.string() }),
    createdBy: v.id("users"),
    createdAt: v.number(),
    rotatedAt: v.optional(v.number()),
  })
    .index("by_org", ["orgId"])
    .index("by_purpose", ["purpose"])
    .index("by_user", ["userId"]),

  // Telegram
  telegramBots: defineTable({
    orgId: v.id("orgs"),
    name: v.string(),
    username: v.string(),
    webhookUrl: v.optional(v.string()),
    webhookSecret: v.string(),
    secretRefId: v.id("secrets"), // encrypted bot token
    status: v.string(), // "active" | "disabled"
  }).index("by_org", ["orgId"]),

  telegramAccounts: defineTable({
    orgId: v.id("orgs"),
    userId: v.id("users"),
    telegramUserId: v.string(),
    username: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    linkedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_telegram_user", ["telegramUserId"]),

  telegramLinkingNonces: defineTable({
    nonce: v.string(),
    userId: v.id("users"),
    orgId: v.id("orgs"),
    expiresAt: v.number(),
    used: v.boolean(),
  }).index("by_nonce", ["nonce"]),

  telegramChats: defineTable({
    orgId: v.id("orgs"),
    chatId: v.string(), // group or DM
    type: v.string(), // "private" | "group" | "supergroup" | "channel"
    title: v.optional(v.string()),
    botId: v.id("telegramBots"),
    routing: v.object({
      linearTeamId: v.optional(v.string()),
      defaultLabels: v.optional(v.array(v.string())),
    }),
  })
    .index("by_org", ["orgId"])
    .index("by_chatId", ["chatId"]),

  // Messaging & processing
  inboundMessages: defineTable({
    orgId: v.id("orgs"),
    chatId: v.string(),
    telegramUpdateId: v.string(),
    fromTelegramUserId: v.string(),
    text: v.string(),
    raw: v.optional(v.object({})),
    status: v.string(), // "queued" | "processed" | "error"
    error: v.optional(v.string()),
    processedAt: v.optional(v.number()),
  })
    .index("by_org", ["orgId"])
    .index("by_chat", ["chatId"])
    .index("by_update", ["telegramUpdateId"]),

  outboundMessages: defineTable({
    orgId: v.id("orgs"),
    chatId: v.string(),
    telegramMessageId: v.optional(v.string()),
    text: v.string(),
    sentAt: v.optional(v.number()),
    status: v.string(), // "queued" | "sent" | "error"
    error: v.optional(v.string()),
  }).index("by_org", ["orgId"]),

  aiRuns: defineTable({
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    inboundMessageId: v.optional(v.id("inboundMessages")),
    model: v.string(),
    promptPreview: v.optional(v.string()),
    responsePreview: v.optional(v.string()),
    usage: v.optional(
      v.object({ prompt_tokens: v.number(), completion_tokens: v.number(), total_tokens: v.number() })
    ),
    status: v.string(), // "succeeded" | "failed"
    error: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_org", ["orgId"])
    .index("by_message", ["inboundMessageId"]),

  issues: defineTable({
    orgId: v.id("orgs"),
    inboundMessageId: v.id("inboundMessages"),
    linearIssueId: v.string(),
    linearUrl: v.string(),
    teamId: v.optional(v.string()),
    title: v.string(),
    status: v.string(), // "created" | "error"
    error: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_org", ["orgId"])
    .index("by_inbound", ["inboundMessageId"])
    .index("by_linear", ["linearIssueId"]),

  routingRules: defineTable({
    orgId: v.id("orgs"),
    name: v.string(),
    isActive: v.boolean(),
    match: v.object({
      chatId: v.optional(v.string()),
      includes: v.optional(v.array(v.string())),
      regex: v.optional(v.string()),
    }),
    action: v.object({
      linearTeamId: v.optional(v.string()),
      labels: v.optional(v.array(v.string())),
      template: v.optional(v.object({ title: v.string(), description: v.string() })),
    }),
    priority: v.number(),
  })
    .index("by_org", ["orgId"])
    .index("by_active_priority", ["isActive", "priority"]),

  webhookEvents: defineTable({
    source: v.string(), // "telegram" | "linear" | "clerk"
    orgId: v.optional(v.id("orgs")),
    receivedAt: v.number(),
    headers: v.optional(v.object({})),
    body: v.optional(v.object({})),
    handled: v.boolean(),
    error: v.optional(v.string()),
  }).index("by_source", ["source"]),

  // Existing payments table
  paymentAttempts: defineTable(paymentAttemptSchemaValidator)
    .index("byPaymentId", ["payment_id"])
    .index("byUserId", ["userId"])
    .index("byPayerUserId", ["payer.user_id"]),
});
