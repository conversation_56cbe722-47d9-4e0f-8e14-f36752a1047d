import { query, QueryCtx, internalQuery } from "./_generated/server";
import { v } from "convex/values";

async function userByExternalId(ctx: QueryCtx, externalId: string) {
  return await ctx.db
    .query("users")
    .withIndex("byExternalId", (q) => q.eq("externalId", externalId))
    .unique();
}

export const getInboundMessage = internalQuery({
  args: { id: v.id("inboundMessages") },
  handler: async (ctx, { id }) => {
    return await ctx.db.get(id);
  },
});

export const getTelegramChatByChatId = internalQuery({
  args: { 
    chatId: v.string(),
    orgId: v.id("orgs"),
  },
  handler: async (ctx, { chatId, orgId }) => {
    return await ctx.db
      .query("telegramChats")
      .withIndex("by_chatId", (q) => q.eq("chatId", chatId))
      .filter((q) => q.eq(q.field("orgId"), orgId))
      .unique();
  },
});

export const getOrg = internalQuery({
  args: { id: v.id("orgs") },
  handler: async (ctx, { id }) => {
    return await ctx.db.get(id);
  },
});

export const listRecentInbound = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    const items = await ctx.db
      .query("inboundMessages")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .collect();
    return items.sort((a, b) => (b._creationTime ?? 0) - (a._creationTime ?? 0)).slice(0, 20);
  },
});

export const listRecentIssues = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    const items = await ctx.db
      .query("issues")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .collect();
    return items.sort((a, b) => b.createdAt - a.createdAt).slice(0, 20);
  },
});

export const getConnectionStatuses = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return { linear: false, openrouter: false, telegram: false };
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return { linear: false, openrouter: false, telegram: false };
    
    // Check Linear connection
    const linearConnection = await ctx.db
      .query("connections")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .filter((q) => q.and(
        q.eq(q.field("provider"), "linear"),
        q.eq(q.field("status"), "connected")
      ))
      .first();
    
    // Check OpenRouter secret
    const openrouterSecret = await ctx.db
      .query("secrets")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .filter((q) => q.eq(q.field("purpose"), "openrouter.key"))
      .first();
    
    // Check Telegram bot
    const telegramBot = await ctx.db
      .query("telegramBots")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();
    
    return {
      linear: !!linearConnection,
      openrouter: !!openrouterSecret,
      telegram: !!telegramBot,
    };
  },
});

export const getConnectionDetails = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return null;
    
    const connections = await ctx.db
      .query("connections")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .collect();
    
    const secrets = await ctx.db
      .query("secrets")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .collect();
    
    const bots = await ctx.db
      .query("telegramBots")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .collect();
    
    return {
      linear: connections.find(c => c.provider === "linear" && c.status === "connected"),
      openrouter: secrets.find(s => s.purpose === "openrouter.key"),
      telegram: bots.find(b => b.status === "active"),
    };
  },
});

export const listRecentAiRuns = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    const items = await ctx.db
      .query("aiRuns")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .collect();
    return items.sort((a, b) => b.createdAt - a.createdAt).slice(0, 50);
  },
});

export const listRecentWebhookEvents = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    const items = await ctx.db
      .query("webhookEvents")
      .filter((q) => q.eq(q.field("orgId"), user.orgId!!))
      .collect();
    return items.sort((a, b) => b.receivedAt - a.receivedAt).slice(0, 50);
  },
});

export const getActivityStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return { messages: 0, issues: 0, aiRuns: 0, webhooks: 0 };
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return { messages: 0, issues: 0, aiRuns: 0, webhooks: 0 };

    const now = Date.now();
    const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000;

    // Count recent activity
    const messages = await ctx.db
      .query("inboundMessages")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .filter((q) => q.gte(q.field("_creationTime"), twentyFourHoursAgo))
      .collect();

    const issues = await ctx.db
      .query("issues")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .filter((q) => q.gte(q.field("createdAt"), twentyFourHoursAgo))
      .collect();

    const aiRuns = await ctx.db
      .query("aiRuns")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!!))
      .filter((q) => q.gte(q.field("createdAt"), twentyFourHoursAgo))
      .collect();

    const webhooks = await ctx.db
      .query("webhookEvents")
      .filter((q) => q.and(
        q.eq(q.field("orgId"), user.orgId!!),
        q.gte(q.field("receivedAt"), twentyFourHoursAgo)
      ))
      .collect();

    return {
      messages: messages.length,
      issues: issues.length,
      aiRuns: aiRuns.length,
      webhooks: webhooks.length,
    };
  },
});

export const getTelegramLinkingStatus = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user) return null;
    
    const telegramAccount = await ctx.db
      .query("telegramAccounts")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .first();
    
    if (!telegramAccount) return null;
    
    return {
      telegramUserId: telegramAccount.telegramUserId,
      username: telegramAccount.username,
      firstName: telegramAccount.firstName,
      lastName: telegramAccount.lastName,
      linkedAt: telegramAccount.linkedAt,
    };
  },
});

export const getBotUsername = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return null;
    
    const bot = await ctx.db
      .query("telegramBots")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();
    
    return bot?.username || null;
  },
});

export const getActivityHistory = query({
  args: { days: v.optional(v.number()) },
  handler: async (ctx, { days = 7 }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    
    const now = Date.now();
    const startTime = now - (days * 24 * 60 * 60 * 1000);
    
    // Get all activity data
    const [messages, issues, aiRuns, webhooks] = await Promise.all([
      ctx.db
        .query("inboundMessages")
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
        .filter((q) => q.gte(q.field("_creationTime"), startTime))
        .collect(),
      ctx.db
        .query("issues") 
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
        .filter((q) => q.gte(q.field("createdAt"), startTime))
        .collect(),
      ctx.db
        .query("aiRuns")
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId!))
        .filter((q) => q.gte(q.field("createdAt"), startTime))
        .collect(),
      ctx.db
        .query("webhookEvents")
        .filter((q) => q.and(
          q.eq(q.field("orgId"), user.orgId!),
          q.gte(q.field("receivedAt"), startTime)
        ))
        .collect()
    ]);
    
    // Group by date
    const dailyData: Record<string, { messages: number; issues: number; aiRuns: number; webhooks: number }> = {};
    
    // Initialize all days with zero counts
    for (let i = 0; i < days; i++) {
      const date = new Date(now - (i * 24 * 60 * 60 * 1000));
      const dateKey = date.toISOString().split('T')[0];
      dailyData[dateKey] = { messages: 0, issues: 0, aiRuns: 0, webhooks: 0 };
    }
    
    // Count messages by day
    messages.forEach(msg => {
      const date = new Date(msg._creationTime).toISOString().split('T')[0];
      if (dailyData[date]) {
        dailyData[date].messages++;
      }
    });
    
    // Count issues by day
    issues.forEach(issue => {
      const date = new Date(issue.createdAt).toISOString().split('T')[0];
      if (dailyData[date]) {
        dailyData[date].issues++;
      }
    });
    
    // Count AI runs by day
    aiRuns.forEach(run => {
      const date = new Date(run.createdAt).toISOString().split('T')[0];
      if (dailyData[date]) {
        dailyData[date].aiRuns++;
      }
    });
    
    // Count webhooks by day
    webhooks.forEach(webhook => {
      const date = new Date(webhook.receivedAt).toISOString().split('T')[0];
      if (dailyData[date]) {
        dailyData[date].webhooks++;
      }
    });
    
    // Convert to array and sort by date
    return Object.entries(dailyData)
      .map(([date, counts]) => ({
        date,
        ...counts,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  },
});

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;
    
    const user = await userByExternalId(ctx, identity.subject);
    return user;
  },
});

export const getOrganizationData = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return null;
    
    const org = await ctx.db.get(user.orgId!);
    return org;
  },
});

export const getOrganizationMembers = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    
    const members = await ctx.db
      .query("users")
      .withIndex("byOrg", (q) => q.eq("orgId", user.orgId!))
      .collect();
    
    return members.sort((a, b) => {
      // Sort by role priority (owner > admin > member), then by name
      const rolePriority = { owner: 3, admin: 2, member: 1 };
      const aRole = (a.role as keyof typeof rolePriority) || 'member';
      const bRole = (b.role as keyof typeof rolePriority) || 'member';
      
      const priorityDiff = (rolePriority[bRole] || 1) - (rolePriority[aRole] || 1);
      if (priorityDiff !== 0) return priorityDiff;
      
      return a.name.localeCompare(b.name);
    });
  },
});

