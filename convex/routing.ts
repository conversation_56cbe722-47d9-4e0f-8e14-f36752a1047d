import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

async function userByExternalId(ctx: any, externalId: string) {
  return await ctx.db
    .query("users")
    .withIndex("byExternalId", (q: any) => q.eq("externalId", externalId))
    .unique();
}

export const listRoutingRules = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    
    const rules = await ctx.db
      .query("routingRules")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId))
      .collect();
    
    return rules.sort((a, b) => a.priority - b.priority);
  },
});

export const createRoutingRule = mutation({
  args: {
    name: v.string(),
    match: v.object({
      chatId: v.optional(v.string()),
      includes: v.optional(v.array(v.string())),
      regex: v.optional(v.string()),
    }),
    action: v.object({
      linearTeamId: v.optional(v.string()),
      labels: v.optional(v.array(v.string())),
      template: v.optional(v.object({
        title: v.string(),
        description: v.string(),
      })),
    }),
    priority: v.optional(v.number()),
  },
  handler: async (ctx, { name, match, action, priority }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");

    // If no priority specified, put it at the end
    if (priority === undefined) {
      const existingRules = await ctx.db
        .query("routingRules")
        .withIndex("by_org", (q) => q.eq("orgId", user.orgId))
        .collect();
      
      priority = existingRules.length > 0 
        ? Math.max(...existingRules.map(r => r.priority)) + 1 
        : 1;
    }

    const ruleId = await ctx.db.insert("routingRules", {
      orgId: user.orgId,
      name,
      isActive: true,
      match,
      action,
      priority,
    });

    return { ruleId };
  },
});

export const updateRoutingRule = mutation({
  args: {
    ruleId: v.id("routingRules"),
    name: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    match: v.optional(v.object({
      chatId: v.optional(v.string()),
      includes: v.optional(v.array(v.string())),
      regex: v.optional(v.string()),
    })),
    action: v.optional(v.object({
      linearTeamId: v.optional(v.string()),
      labels: v.optional(v.array(v.string())),
      template: v.optional(v.object({
        title: v.string(),
        description: v.string(),
      })),
    })),
    priority: v.optional(v.number()),
  },
  handler: async (ctx, { ruleId, name, isActive, match, action, priority }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");

    // Verify the rule belongs to this organization
    const rule = await ctx.db.get(ruleId);
    if (!rule || rule.orgId !== user.orgId) {
      throw new Error("Routing rule not found");
    }

    const updates: any = {};
    if (name !== undefined) updates.name = name;
    if (isActive !== undefined) updates.isActive = isActive;
    if (match !== undefined) updates.match = match;
    if (action !== undefined) updates.action = action;
    if (priority !== undefined) updates.priority = priority;

    await ctx.db.patch(ruleId, updates);
    return { success: true };
  },
});

export const deleteRoutingRule = mutation({
  args: {
    ruleId: v.id("routingRules"),
  },
  handler: async (ctx, { ruleId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");

    // Verify the rule belongs to this organization
    const rule = await ctx.db.get(ruleId);
    if (!rule || rule.orgId !== user.orgId) {
      throw new Error("Routing rule not found");
    }

    await ctx.db.delete(ruleId);
    return { success: true };
  },
});

export const reorderRoutingRules = mutation({
  args: {
    ruleIds: v.array(v.id("routingRules")),
  },
  handler: async (ctx, { ruleIds }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");

    // Update priority for each rule based on new order
    for (let i = 0; i < ruleIds.length; i++) {
      const ruleId = ruleIds[i];
      const rule = await ctx.db.get(ruleId);
      
      if (!rule || rule.orgId !== user.orgId) {
        throw new Error(`Invalid rule ID: ${ruleId}`);
      }

      await ctx.db.patch(ruleId, { priority: i + 1 });
    }

    return { success: true };
  },
});

export const testRoutingRule = query({
  args: {
    testMessage: v.string(),
    testChatId: v.optional(v.string()),
  },
  handler: async (ctx, { testMessage, testChatId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];
    
    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) return [];
    
    const rules = await ctx.db
      .query("routingRules")
      .withIndex("by_org", (q) => q.eq("orgId", user.orgId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
    
    const sortedRules = rules.sort((a, b) => a.priority - b.priority);
    const matchingRules = [];
    
    for (const rule of sortedRules) {
      let matches = true;
      const reasons = [];
      
      // Check chat ID match
      if (rule.match.chatId && testChatId) {
        if (rule.match.chatId !== testChatId) {
          matches = false;
          reasons.push(`Chat ID doesn't match (expected: ${rule.match.chatId}, got: ${testChatId})`);
        } else {
          reasons.push(`✓ Chat ID matches`);
        }
      }
      
      // Check keyword includes
      if (rule.match.includes && rule.match.includes.length > 0) {
        const messageText = testMessage.toLowerCase();
        const foundKeywords = rule.match.includes.filter(keyword => 
          messageText.includes(keyword.toLowerCase())
        );
        
        if (foundKeywords.length === 0) {
          matches = false;
          reasons.push(`No keywords found (looking for: ${rule.match.includes.join(', ')})`);
        } else {
          reasons.push(`✓ Found keywords: ${foundKeywords.join(', ')}`);
        }
      }
      
      // Check regex
      if (rule.match.regex) {
        try {
          const regex = new RegExp(rule.match.regex, 'i');
          if (!regex.test(testMessage)) {
            matches = false;
            reasons.push(`Regex doesn't match (pattern: ${rule.match.regex})`);
          } else {
            reasons.push(`✓ Regex matches`);
          }
        } catch (error) {
          matches = false;
          reasons.push(`Invalid regex pattern: ${rule.match.regex}`);
        }
      }
      
      matchingRules.push({
        rule: {
          _id: rule._id,
          name: rule.name,
          priority: rule.priority,
        },
        matches,
        reasons,
      });
    }
    
    return matchingRules;
  },
});