import { internalAction, internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { api, internal } from "./_generated/api";

export const processMessage = internalAction({
  args: { inboundMessageId: v.id("inboundMessages") },
  handler: async (ctx, { inboundMessageId }) => {
    const inbound = await ctx.runQuery(internal.queries.getInboundMessage, { id: inboundMessageId });
    if (!inbound) return;

    try {
      // Get chat configuration and routing rules
      const chat = await ctx.runQuery(internal.queries.getTelegramChatByChatId, { 
        chatId: inbound.chatId,
        orgId: inbound.orgId,
      });

      const org = await ctx.runQuery(internal.queries.getOrg, { id: inbound.orgId });
      const linearTeamId = chat?.routing?.linearTeamId ?? org?.settings?.defaultLinearTeamId;
      const defaultLabels = chat?.routing?.defaultLabels ?? [];

      // Enhanced AI extraction with context
      const aiResult = await ctx.runAction((api as any)["integrations/openrouter"].extractTaskFromMessage, {
        orgId: inbound.orgId,
        userId: undefined, // TODO: resolve user by telegram user id
        inboundMessageId,
        message: inbound.text,
        context: {
          chatTitle: (inbound.raw as any)?.message?.chat?.title,
          senderName: (inbound.raw as any)?.message?.from?.first_name || (inbound.raw as any)?.message?.from?.username,
          linearTeamId,
          defaultLabels,
        },
      });

      const tasks = aiResult.tasks || [];
      if (tasks.length === 0) {
        // No actionable tasks found - send acknowledgment
        await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
          orgId: inbound.orgId,
          chatId: inbound.chatId,
          text: "👍 Message received, but no actionable tasks detected.",
        });
        await ctx.runMutation(internal.telegram.updateInboundStatus, {
          id: inboundMessageId,
          status: "processed",
          processedAt: Date.now(),
        });
        return;
      }

      // Create Linear issues for all extracted tasks
      const issueResults = await ctx.runAction((api as any)["integrations/linear"].createIssuesFromTasks, {
        orgId: inbound.orgId,
        userId: undefined,
        inboundMessageId,
        tasks,
      });

      const issues = issueResults.issues || [];
      const successCount = issues.filter((issue: any) => issue.issueId && !issue.error).length;
      const errorCount = issues.length - successCount;

      // Prepare confirmation message
      let confirmationText = "";
      if (successCount > 0) {
        if (successCount === 1) {
          const issue = issues.find((i: any) => i.issueId && !i.error);
          confirmationText = `✅ Created Linear issue: ${issue?.issueUrl || issue?.issueId}`;
          if (issue?.teamName) {
            confirmationText += `\n📁 Team: ${issue.teamName}`;
          }
        } else {
          confirmationText = `✅ Created ${successCount} Linear issues:`;
          issues
            .filter((issue: any) => issue.issueId && !issue.error)
            .forEach((issue: any, i: number) => {
              confirmationText += `\n${i + 1}. ${issue.issueUrl || issue.issueId}`;
            });
        }
      }

      if (errorCount > 0) {
        if (confirmationText) confirmationText += "\n\n";
        confirmationText += `❌ ${errorCount} task${errorCount > 1 ? 's' : ''} failed to create`;
        
        const errors = issues
          .filter((issue: any) => issue.error)
          .map((issue: any) => issue.error)
          .filter(Boolean);
        
        if (errors.length > 0) {
          // Show first error for debugging, don't overwhelm chat
          confirmationText += `:\n${errors[0]}`;
        }
      }

      // Send confirmation to Telegram
      await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
        orgId: inbound.orgId,
        chatId: inbound.chatId,
        text: confirmationText || "⚠️ No issues were created.",
      });

      // Update message status
      await ctx.runMutation(internal.telegram.updateInboundStatus, {
        id: inboundMessageId,
        status: "processed",
        processedAt: Date.now(),
      });

    } catch (err: any) {
      console.error("Message processing failed:", err);
      
      // Send error message to Telegram
      await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
        orgId: inbound.orgId,
        chatId: inbound.chatId,
        text: "❌ Failed to process message. Please try again or contact support.",
      });

      // Update message with error status
      await ctx.runMutation(internal.telegram.updateInboundStatus, {
        id: inboundMessageId,
        status: "error",
        error: String(err),
      });
    }
  },
});

export const updateInboundStatus = internalMutation({
  args: {
    id: v.id("inboundMessages"),
    status: v.string(),
    error: v.optional(v.string()),
    processedAt: v.optional(v.number()),
  },
  handler: async (ctx, { id, status, error, processedAt }) => {
    await ctx.db.patch(id, {
      status,
      error,
      processedAt,
    });
  },
});

export const handleStartCommand = internalAction({
  args: {
    chatId: v.string(),
    telegramUserId: v.string(),
    username: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    startParameter: v.optional(v.string()),
    orgId: v.id("orgs"),
  },
  handler: async (ctx, { chatId, telegramUserId, username, firstName, lastName, startParameter, orgId }) => {
    if (!startParameter) {
      // No linking nonce, send generic welcome message
      await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
        orgId,
        chatId,
        text: "👋 Welcome to BuddyTasks Pro! Send me any task and I'll create a Linear issue for you.",
      });
      return;
    }

    // Handle account linking with nonce
    const nonce = await ctx.runQuery(internal.telegram.findLinkingNonce, { nonce: startParameter });
    if (!nonce) {
      await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
        orgId,
        chatId,
        text: "❌ Invalid or expired linking code. Please try again from the app.",
      });
      return;
    }

    if (nonce.used || nonce.expiresAt < Date.now()) {
      await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
        orgId,
        chatId,
        text: "❌ This linking code has expired. Please generate a new one from the app.",
      });
      return;
    }

    // Mark nonce as used
    await ctx.runMutation(internal.telegram.markNonceUsed, { nonceId: nonce._id });

    // Create the account link
    try {
      await ctx.runMutation(internal.telegram.createTelegramAccountLink, {
        userId: nonce.userId,
        orgId: nonce.orgId,
        telegramUserId,
        username,
        firstName,
        lastName,
      });

      await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
        orgId,
        chatId,
        text: "✅ Your Telegram account has been successfully linked! You can now send me tasks and I'll create Linear issues for you.",
      });
    } catch (error) {
      await ctx.runAction((api as any)["integrations/telegram"].sendMessage, {
        orgId,
        chatId,
        text: `❌ Failed to link your account: ${error}`,
      });
    }
  },
});

export const findLinkingNonce = internalQuery({
  args: { nonce: v.string() },
  handler: async (ctx: any, { nonce }: { nonce: string }) => {
    return await ctx.db
      .query("telegramLinkingNonces")
      .withIndex("by_nonce", (q: any) => q.eq("nonce", nonce))
      .first();
  },
});

export const markNonceUsed = internalMutation({
  args: { nonceId: v.id("telegramLinkingNonces") },
  handler: async (ctx, { nonceId }) => {
    await ctx.db.patch(nonceId, { used: true });
  },
});

export const createTelegramAccountLink = internalMutation({
  args: {
    userId: v.id("users"),
    orgId: v.id("orgs"),
    telegramUserId: v.string(),
    username: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, { userId, orgId, telegramUserId, username, firstName, lastName }) => {
    // Check if this Telegram account is already linked
    const existingAccount = await ctx.db
      .query("telegramAccounts")
      .withIndex("by_telegram_user", (q) => q.eq("telegramUserId", telegramUserId))
      .first();
    
    if (existingAccount && existingAccount.userId !== userId) {
      throw new Error("This Telegram account is already linked to another user");
    }

    if (existingAccount) {
      // Update existing link
      await ctx.db.patch(existingAccount._id, {
        username,
        firstName,
        lastName,
        linkedAt: Date.now(),
      });
      return existingAccount._id;
    } else {
      // Create new link
      return await ctx.db.insert("telegramAccounts", {
        orgId,
        userId,
        telegramUserId,
        username,
        firstName,
        lastName,
        linkedAt: Date.now(),
      });
    }
  },
});

// Optional internal action to register a Telegram webhook in the future
export const setWebhook = internalAction({
  args: { botId: v.id("telegramBots"), url: v.string() },
  handler: async (ctx, { botId, url }) => {
    // Placeholder: perform Telegram setWebhook call using decrypted bot token
    return { ok: true, url };
  },
});

export const createOrUpdateBot = internalMutation({
  args: {
    orgId: v.id("orgs"),
    username: v.optional(v.string()),
    name: v.optional(v.string()),
    webhookSecret: v.string(),
    secretRefId: v.id("secrets"),
  },
  handler: async (ctx, { orgId, username, name, webhookSecret, secretRefId }) => {
    const existing = await ctx.db.query("telegramBots").withIndex("by_org", (q) => q.eq("orgId", orgId)).first();
    if (existing) {
      await ctx.db.patch(existing._id, { username, name, webhookSecret, secretRefId, status: "active" });
      return existing._id;
    }
    return await ctx.db.insert("telegramBots", {
      orgId,
      name: name ?? "Bot",
      username: username ?? "",
      webhookUrl: undefined,
      webhookSecret,
      secretRefId,
      status: "active",
    });
  },
});

export const insertOutboundMessage = internalMutation({
  args: {
    orgId: v.id("orgs"),
    chatId: v.string(),
    text: v.string(),
  },
  handler: async (ctx, { orgId, chatId, text }) => {
    return await ctx.db.insert("outboundMessages", {
      orgId,
      chatId,
      telegramMessageId: undefined,
      text,
      sentAt: undefined,
      status: "queued",
      error: undefined,
    });
  },
});

export const markOutboundStatus = internalMutation({
  args: {
    id: v.id("outboundMessages"),
    status: v.string(),
    error: v.optional(v.string()),
    telegramMessageId: v.optional(v.string()),
  },
  handler: async (ctx, { id, status, error, telegramMessageId }) => {
    await ctx.db.patch(id, {
      status,
      error,
      telegramMessageId,
      sentAt: status === "sent" ? Date.now() : undefined,
    });
  },
});
