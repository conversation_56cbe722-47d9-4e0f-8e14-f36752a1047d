import { mutation, QueryCtx, internalQuery } from "./_generated/server";
import { v } from "convex/values";

async function userByExternalId(ctx: QueryCtx, externalId: string) {
  return await ctx.db
    .query("users")
    .withIndex("byExternalId", (q) => q.eq("externalId", externalId))
    .unique();
}

export const ensureUserOrg = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    let user = await userByExternalId(ctx, identity.subject);
    if (!user) {
      const name = identity.name ?? identity.givenName ?? "User";
      const id = await ctx.db.insert("users", { name, externalId: identity.subject, role: "owner" });
      user = { _id: id, name, externalId: identity.subject, orgId: undefined, role: "owner" } as any;
    }

    if (user?.orgId) {
      return { orgId: user.orgId, userId: user._id };
    }

    const orgName = `${user?.name.split(" ")[0]}'s Org`;
    const orgId = await ctx.db.insert("orgs", {
      name: orgName,
      clerkOrganizationId: undefined,
      plan: "free",
      settings: { defaultModel: "openrouter/auto", defaultLinearTeamId: undefined, allowUserBYOKeys: true },
    });
    await ctx.db.patch(user!._id, { orgId });
    return { orgId, userId: user!._id };
  },
});

export const updateOrganization = mutation({
  args: {
    name: v.string(),
    settings: v.object({
      defaultModel: v.optional(v.string()),
      defaultLinearTeamId: v.optional(v.string()),
      allowUserBYOKeys: v.boolean(),
    }),
  },
  handler: async (ctx, { name, settings }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await userByExternalId(ctx, identity.subject);
    if (!user?.orgId) throw new Error("No organization found");

    // Check if user has permission to update organization settings
    if (user.role !== "owner") {
      throw new Error("Only organization owners can update organization settings");
    }

    await ctx.db.patch(user.orgId, {
      name,
      settings,
    });

    return { success: true };
  },
});

export const updateUserRole = mutation({
  args: {
    userId: v.id("users"),
    role: v.string(),
  },
  handler: async (ctx, { userId, role }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const currentUser = await userByExternalId(ctx, identity.subject);
    if (!currentUser?.orgId) throw new Error("No organization found");

    // Check permissions
    if (currentUser.role !== "owner" && currentUser.role !== "admin") {
      throw new Error("Only owners and admins can update user roles");
    }

    // Get the target user
    const targetUser = await ctx.db.get(userId);
    if (!targetUser || targetUser.orgId !== currentUser.orgId) {
      throw new Error("User not found in organization");
    }

    // Prevent non-owners from creating owners
    if (role === "owner" && currentUser.role !== "owner") {
      throw new Error("Only owners can assign owner role");
    }

    // Prevent self role changes for owners
    if (targetUser._id === currentUser._id && currentUser.role === "owner") {
      throw new Error("Owners cannot change their own role");
    }

    await ctx.db.patch(userId, { role });
    return { success: true };
  },
});

export const removeUserFromOrg = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, { userId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const currentUser = await userByExternalId(ctx, identity.subject);
    if (!currentUser?.orgId) throw new Error("No organization found");

    // Check permissions
    if (currentUser.role !== "owner" && currentUser.role !== "admin") {
      throw new Error("Only owners and admins can remove users");
    }

    // Get the target user
    const targetUser = await ctx.db.get(userId);
    if (!targetUser || targetUser.orgId !== currentUser.orgId) {
      throw new Error("User not found in organization");
    }

    // Prevent removing yourself
    if (targetUser._id === currentUser._id) {
      throw new Error("Cannot remove yourself from the organization");
    }

    // Prevent removing owners (unless you're an owner)
    if (targetUser.role === "owner" && currentUser.role !== "owner") {
      throw new Error("Only owners can remove other owners");
    }

    // Remove user from organization
    await ctx.db.patch(userId, { orgId: undefined, role: undefined });
    return { success: true };
  },
});

export const getById = internalQuery({
  args: { orgId: v.id("orgs") },
  handler: async (ctx, { orgId }) => {
    return await ctx.db.get(orgId);
  },
});

