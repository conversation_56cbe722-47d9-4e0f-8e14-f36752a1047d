import { action, internalMutation, internalQuery } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";

async function getAesKey() {
  const base64 = process.env.APP_ENCRYPTION_KEY;
  if (!base64) throw new Error("APP_ENCRYPTION_KEY not set");
  const raw = Buffer.from(base64, "base64");
  return await crypto.subtle.importKey(
    "raw",
    raw,
    { name: "AES-GCM", length: 256 },
    false,
    ["encrypt", "decrypt"],
  );
}

export const saveSecret = action({
  args: {
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    purpose: v.string(),
    secret: v.string(),
    createdBy: v.id("users"),
  },
  returns: v.object({ secretId: v.id("secrets") }),
  handler: async (ctx, { orgId, userId, purpose, secret, createdBy }): Promise<any> => {
    const key = await getAes<PERSON>ey();
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const enc = new TextEncoder().encode(secret);
    const ciphertextBuf = (await crypto.subtle.encrypt({ name: "AES-GCM", iv }, key, enc)) as ArrayBuffer;
    const ciphertext = new Uint8Array(ciphertextBuf);

    const secretId: any = await ctx.runMutation(internal.secrets.internalSaveSecret, {
      orgId,
      userId,
      purpose,
      algorithm: "AES-256-GCM",
      ciphertext,
      iv,
      tag: new Uint8Array(),
      keyVersion: 1,
      wrappedKey: new Uint8Array(),
      wrapping: { method: "local-kek", keyId: "app-key-v1" },
      createdBy,
      createdAt: Date.now(),
      rotatedAt: undefined,
    });

    return { secretId };
  },
});

export const internalSaveSecret = internalMutation({
  args: {
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    purpose: v.string(),
    algorithm: v.string(),
    ciphertext: v.bytes(),
    iv: v.bytes(),
    tag: v.bytes(),
    keyVersion: v.number(),
    wrappedKey: v.bytes(),
    wrapping: v.object({ method: v.string(), keyId: v.string() }),
    createdBy: v.id("users"),
    createdAt: v.number(),
    rotatedAt: v.optional(v.number()),
  },
  handler: async (ctx, values) => {
    return await ctx.db.insert("secrets", values);
  },
});

export const internalFindSecret = internalQuery({
  args: { orgId: v.id("orgs"), purpose: v.string(), userId: v.optional(v.id("users")) },
  handler: async (ctx, { orgId, purpose, userId }) => {
    if (userId) {
      const userScoped = await ctx.db
        .query("secrets")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .filter((q) => q.eq(q.field("purpose"), purpose))
        .first();
      if (userScoped) return userScoped;
    }
    const orgScoped = await ctx.db
      .query("secrets")
      .withIndex("by_org", (q) => q.eq("orgId", orgId))
      .filter((q) => q.eq(q.field("purpose"), purpose))
      .first();
    return orgScoped ?? null;
  },
});

export const getSecretValue = action({
  args: { orgId: v.id("orgs"), purpose: v.string(), userId: v.optional(v.id("users")) },
  handler: async (ctx, { orgId, purpose, userId }) => {
    const base64 = process.env.APP_ENCRYPTION_KEY;
    if (!base64) throw new Error("APP_ENCRYPTION_KEY not set");
    const raw = Buffer.from(base64, "base64");
    const key = await crypto.subtle.importKey("raw", raw, { name: "AES-GCM", length: 256 }, false, ["decrypt"]);
    const secretDoc = await ctx.runQuery(internal.secrets.internalFindSecret, { orgId, purpose, userId });
    if (!secretDoc) return null;
    const plaintextBuf = await crypto.subtle.decrypt({ name: "AES-GCM", iv: secretDoc.iv }, key, secretDoc.ciphertext);
    return new TextDecoder().decode(plaintextBuf);
  },
});

// Export alias for backward compatibility
export const findSecret = internalFindSecret;
