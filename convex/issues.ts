import { internalMutation } from "./_generated/server";
import { v } from "convex/values";

export const create = internalMutation({
  args: {
    orgId: v.id("orgs"),
    inboundMessageId: v.id("inboundMessages"),
    linearIssueId: v.string(),
    linearUrl: v.string(),
    teamId: v.optional(v.string()),
    title: v.string(),
    status: v.string(),
    error: v.optional(v.string()),
    createdAt: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("issues", args as any);
  },
});