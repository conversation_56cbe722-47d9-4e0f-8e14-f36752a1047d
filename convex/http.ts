import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import type { WebhookEvent } from "@clerk/backend";
import { Webhook } from "svix";
import { transformWebhookData } from "./paymentAttemptTypes";

const http = httpRouter();

http.route({
  path: "/clerk-users-webhook",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const event = await validateRequest(request);
    if (!event) {
      return new Response("Error occured", { status: 400 });
    }
    switch ((event as any).type) {
      case "user.created": // intentional fallthrough
      case "user.updated":
        await ctx.runMutation(internal.users.upsertFromClerk, {
          data: event.data as any,
        });
        break;

      case "user.deleted": {
        const clerkUserId = (event.data as any).id!;
        await ctx.runMutation(internal.users.delete<PERSON><PERSON><PERSON><PERSON><PERSON>, { clerkUserId });
        break;
      }

      case "paymentAttempt.updated": {
        const paymentAttemptData = transformWebhookData((event as any).data);
        await ctx.runMutation(internal.paymentAttempts.savePaymentAttempt, {
          paymentAttemptData,
        });
        break;
      }
      

      
      default:
        console.log("Ignored webhook event", (event as any).type);
    }

    return new Response(null, { status: 200 });
  }),
});

// Telegram webhook endpoint per PRD
http.route({
  path: "/telegram/webhook",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const secretHeader = request.headers.get("X-Telegram-Bot-Api-Secret-Token");
    const now = Date.now();
    let body: any = {};
    let orgId: Id<"orgs"> | undefined = undefined;
    
    const headersObj: Record<string, string> = {};
    // Avoid Headers.entries() for DOM.Iterable type issues
    request.headers.forEach((value, key) => {
      headersObj[key] = value;
    });
    
    try {
      body = await request.json();
    } catch (e) {
      // Log parsing error and return immediately
      await ctx.runMutation(internal.webhookEvents.create, {
        source: "telegram",
        orgId: undefined,
        receivedAt: now,
        headers: headersObj as any,
        body: { error: "Failed to parse JSON" } as any,
        handled: false,
        error: "Invalid JSON payload",
      });
      return new Response("Invalid JSON", { status: 400 });
    }

    // Validate secret token by matching any configured bot
    const bots = await ctx.runQuery(internal.telegramBots.getAll);
    const matchedBot = bots.find((b: any) => b.webhookSecret === secretHeader);
    if (!matchedBot) {
      await ctx.runMutation(internal.webhookEvents.create, {
        source: "telegram",
        orgId: undefined,
        receivedAt: now,
        headers: headersObj as any,
        body: body as any,
        handled: false,
        error: "Unauthorized: Invalid webhook secret",
      });
      return new Response("Unauthorized", { status: 401 });
    }
    
    orgId = matchedBot.orgId;

    const message = body?.message ?? body?.edited_message;
    const chatId = message?.chat?.id?.toString?.();
    const fromId = message?.from?.id?.toString?.();
    const text = message?.text ?? "";
    const updateId = (body?.update_id ?? "").toString();
    
    // Extract user info for potential account linking
    const username = message?.from?.username;
    const firstName = message?.from?.first_name;
    const lastName = message?.from?.last_name;

    // Log successful webhook receipt
    await ctx.runMutation(internal.webhookEvents.create, {
      source: "telegram",
      orgId,
      receivedAt: now,
      headers: headersObj as any,
      body: body as any,
      handled: true,
      error: undefined,
    });

    // Skip non-text messages
    if (!text || !chatId || !fromId || !updateId) {
      return new Response("OK", { status: 200 });
    }

    // Handle /start commands specially (for account linking)
    if (text.startsWith("/start")) {
      const startParameter = text.replace("/start", "").trim();
      
      await ctx.scheduler.runAfter(0, internal.telegram.handleStartCommand, {
        chatId,
        telegramUserId: fromId,
        username,
        firstName,
        lastName,
        startParameter: startParameter || undefined,
        orgId,
      });
      
      return new Response("OK", { status: 200 });
    }

    // Insert inbound message and schedule processing for regular messages
    const inboundId = await ctx.runMutation(internal.inboundMessages.create, {
      orgId,
      chatId,
      telegramUpdateId: updateId,
      fromTelegramUserId: fromId,
      text,
      raw: { message } as any,
      status: "queued",
      error: undefined,
      processedAt: undefined,
    });

    // Schedule async processing (decouple from webhook response)
    await ctx.scheduler.runAfter(0, internal.telegram.processMessage, {
      inboundMessageId: inboundId,
    });

    return new Response("OK", { status: 200 });
  }),
});

async function validateRequest(req: Request): Promise<WebhookEvent | null> {
  const payloadString = await req.text();
  const svixHeaders = {
    "svix-id": req.headers.get("svix-id")!,
    "svix-timestamp": req.headers.get("svix-timestamp")!,
    "svix-signature": req.headers.get("svix-signature")!,
  };
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET!);
  try {
    return wh.verify(payloadString, svixHeaders) as unknown as WebhookEvent;
  } catch (error) {
    console.error("Error verifying webhook event", error);
    return null;
  }
}

export default http;
