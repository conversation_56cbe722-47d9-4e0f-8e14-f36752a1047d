import { internalMutation } from "./_generated/server";
import { v } from "convex/values";

export const create = internalMutation({
  args: {
    source: v.string(),
    orgId: v.optional(v.id("orgs")),
    receivedAt: v.number(),
    headers: v.any(),
    body: v.any(),
    handled: v.boolean(),
    error: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("webhookEvents", args as any);
  },
});
