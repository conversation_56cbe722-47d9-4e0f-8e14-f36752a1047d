# Repository Guidelines

## Project Structure & Module Organization
- `app/`: Next.js App Router pages and layouts. Key areas: `(landing)/`, `dashboard/`, `layout.tsx`, `globals.css`, `not-found.tsx`.
- `components/`: UI building blocks (e.g., `ui/`, `magicui/`, `motion-primitives/`, `react-bits/`), providers like `ConvexClientProvider.tsx`, and theming via `theme-provider.tsx`.
- `convex/`: Real-time backend (schema, queries, mutations, webhooks). Start here for data: `schema.ts`, `users.ts`, `paymentAttempts.ts`, `http.ts`.
- `hooks/` and `lib/`: Reusable hooks (e.g., `use-mobile.ts`) and utilities (`lib/utils.ts`).
- `public/`: Static assets (icons, images). `docs/`: Product and rules docs.
- `middleware.ts`: Route protection and auth wiring.

## Build, Test, and Development Commands
- `cp .env.example .env.local`: Bootstrap local env; fill Clerk and Convex vars.
- `npx convex dev`: Initialize and run Convex locally; sets required Convex env.
- `npm run dev`: Start Next.js dev server (Turbopack) at `http://localhost:3000`.
- `npm run build`: Production build. `npm start`: Serve built app.
- `npm run lint`: Run Next.js/ESLint checks.

Use a single package manager; npm is assumed in scripts.

## Coding Style & Naming Conventions
- **Language**: TypeScript (strict). Indentation: 2 spaces.
- **Components**: Export with PascalCase; filenames generally kebab-case (e.g., `custom-clerk-pricing.tsx`).
- **Hooks**: `use-*.ts` (e.g., `hooks/use-mobile.ts`). Utilities live in `lib/`.
- **Pages/Layouts**: Follow App Router patterns (`page.tsx`, `layout.tsx`).
- **Styling**: Tailwind CSS v4 + shadcn/ui. Prefer utility classes and the `cn` helper in `lib/utils.ts`.
- **Linting**: Keep `npm run lint` clean before pushing.

## Testing Guidelines
- No test runner is configured yet. If adding tests, colocate as `*.test.ts(x)` next to code and document manual verification steps in the PR. Keep tests deterministic and avoid network calls.

## Commit & Pull Request Guidelines
- **Commits**: Prefer Conventional Commits (e.g., `feat:`, `fix:`, `chore:`) with concise, imperative subjects.
- **Branches**: `feat/short-description`, `fix/issue-key`, `chore/...`.
- **PRs**: Include a clear summary, linked issues, screenshots for UI changes, env/DB notes (if any), reproduction and test steps. PRs must build and pass lint locally.

## Security & Configuration Tips
- Never commit secrets. Use `.env.local` and Convex/Clerk dashboards. Only expose safe values via `NEXT_PUBLIC_*`.
- Webhooks are verified in `convex/http.ts` (Svix). Keep handlers minimal and validate payloads.
- Sensitive data access belongs in Convex functions; client code should call Convex APIs, not read secrets directly.

